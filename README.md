# Global Humanitarian Action (GAHA) Website

A modern, responsive website for Global Humanitarian Action (GAHA) - a humanitarian organization dedicated to providing essential support to vulnerable communities through food assistance, education sponsorship, and emergency relief programs across Kenya and East Africa.

## 🌟 Features

### Frontend (React + Vite)
- **Modern UI Design**: Glassmorphic navigation with professional styling
- **Hero Carousel**: Dynamic image carousel with 4 rotating slides
- **Interactive Elements**: 
  - Floating WhatsApp chatbot with smart responses
  - Floating WhatsApp contact button
  - Animated counters and statistics
  - Smooth animations and transitions
- **Responsive Design**: Mobile-first approach, works on all devices
- **Payment Integration**: M-Pesa STK Push for donations and job applications
- **Professional Pages**:
  - Home with impact statistics
  - About with team profiles
  - Programs with detailed descriptions
  - Careers with job listings
  - Donate with payment forms
  - Contact with multiple communication channels
  - Job application system

### Backend (Laravel)
- **RESTful API**: Clean API endpoints for all functionality
- **M-Pesa Integration**: Complete Daraja API integration for payments
- **Database**: MySQL with proper schema for users, jobs, applications, and payments
- **Authentication**: User management system
- **File Upload**: CV upload functionality for job applications

## 🚀 Quick Start

### Frontend Setup
```bash
cd frontend
npm install
npm run dev
```

### Backend Setup
```bash
cd backend
composer install
cp .env.example .env
php artisan key:generate
php artisan migrate
```

## 📱 Key Features

### Navigation
- **Top Bar**: Glassmorphic design with phone number and CTA buttons
- **Main Nav**: Clean navigation with smooth hover effects
- **Mobile Responsive**: Perfect on all screen sizes

### WhatsApp Integration
- **Floating Chatbot**: Interactive assistant with smart responses
- **Direct Contact**: Floating WhatsApp button for instant messaging
- **Quick Replies**: Pre-defined responses for common queries

### Hero Carousel
- **4 Dynamic Slides**: Rotating content with different messages
- **Auto-rotation**: Changes every 5 seconds
- **Manual Control**: Clickable navigation dots
- **High-quality Images**: Professional humanitarian imagery

### Animations
- **Counter Animations**: Animated counting to target numbers
- **Fade-in Effects**: Staggered animations for cards
- **Hover Effects**: Interactive elements throughout
- **Loading States**: Professional loading indicators

## 🎨 Design System

### Colors
- **Primary Blue**: #1e40af
- **Primary Green**: #059669  
- **Accent Orange**: #ea580c
- **Neutral Palette**: Professional grays and whites

### Typography
- **Font**: Inter (Google Fonts)
- **Hierarchy**: Clear heading and body text structure
- **Responsive**: Scales appropriately on all devices

## 💳 Payment Integration

### M-Pesa Features
- **STK Push**: Direct mobile payment integration
- **Real-time Status**: Payment status tracking
- **Webhook Support**: Automatic payment confirmation
- **Multi-purpose**: Supports donations and job application fees

## 📊 Impact Metrics

The website showcases GAHA's impact:
- **500+ Families Supported**
- **150+ Children Educated**
- **50+ Emergency Responses**
- **25+ Communities Transformed**

## 🛠️ Technology Stack

### Frontend
- React 18
- Vite (Build Tool)
- React Router (Navigation)
- Axios (HTTP Client)
- CSS3 (Styling with Custom Properties)

### Backend
- Laravel 10
- PHP 8.1+
- MySQL Database
- M-Pesa Daraja API
- Guzzle HTTP Client

## 📁 Project Structure

```
globalhumanitarianaction_complete/
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── HeroCarousel.jsx
│   │   │   └── WhatsAppChatbot.jsx
│   │   ├── pages/
│   │   │   ├── Home.jsx
│   │   │   ├── About.jsx
│   │   │   ├── Programs.jsx
│   │   │   ├── Careers.jsx
│   │   │   ├── Donate.jsx
│   │   │   ├── Contact.jsx
│   │   │   └── Apply.jsx
│   │   ├── main.jsx
│   │   ├── config.js
│   │   └── styles.css
│   ├── index.html
│   └── package.json
├── backend/
│   ├── app/
│   │   ├── Http/Controllers/
│   │   ├── Models/
│   │   └── Services/
│   ├── database/
│   ├── routes/
│   └── composer.json
└── docs/
    └── setup_guide.md
```

## 🌐 Deployment

### Frontend Deployment
1. Build the project: `npm run build`
2. Upload `dist/` contents to your web server
3. Configure your domain to point to the built files

### Backend Deployment
1. Upload backend files to your server
2. Point your domain to `backend/public`
3. Configure environment variables
4. Run database migrations

## 📞 Contact Information

- **Phone**: +254 700 000 000
- **Email**: <EMAIL>
- **WhatsApp**: +254 700 000 002
- **Address**: Westlands Business Centre, Nairobi, Kenya

## 🤝 Contributing

This project is designed for Global Humanitarian Action. For contributions or modifications, please contact the organization directly.

## 📄 License

This project is proprietary to Global Humanitarian Action. All rights reserved.

---

**Built with ❤️ for humanitarian impact**
# global-humanitarian-action-website
# GlobalHumanitarianAction1
