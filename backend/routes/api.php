<?php
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\JobsController;
use App\Http\Controllers\ApplicationsController;
use App\Http\Controllers\PaymentsController;

Route::get('/jobs',[JobsController::class,'index']);
Route::get('/jobs/{id}',[JobsController::class,'show']);
Route::post('/applications',[ApplicationsController::class,'store']);
Route::post('/payments/initiate',[PaymentsController::class,'initiate']);
Route::get('/payments/status/{checkout_id}',[PaymentsController::class,'status']);
Route::post('/mpesa/callback',[PaymentsController::class,'mpesaCallback']);
