-- SQL schema for <PERSON><PERSON>
CREATE TABLE users (id INT AUTO_INCREMENT PRIMARY KEY, fullname VARCHA<PERSON>(150), email VARCHAR(191), phone VARCHAR(30), password VARCHAR(255), role VARCHAR(20) DEFAULT 'member', created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP);
CREATE TABLE jobs (id INT AUTO_INCREMENT PRIMARY KEY, title VARCHAR(200), description TEXT, location VARCHAR(100), slots INT DEFAULT 1, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, open_until DATE);
CREATE TABLE applications (id INT AUTO_INCREMENT PRIMARY KEY, user_id INT, job_id INT, cv_path VARCHAR(255), cover_letter TEXT, status VARCHAR(20) DEFAULT 'submitted', submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP);
CREATE TABLE payments (id INT AUTO_INCREMENT PRIMARY KEY, user_id INT, type VA<PERSON>HA<PERSON>(30), provider VARCHAR(30), provider_txn_id VARCHAR(255), amount DECIMAL(10,2), currency VARCHAR(10) DEFAULT 'KES', status VARCHAR(20) DEFAULT 'pending', created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP);
CREATE TABLE donations (id INT AUTO_INCREMENT PRIMARY KEY, user_id INT, amount DECIMAL(10,2), currency VARCHAR(10) DEFAULT 'KES', method VARCHAR(50), message VARCHAR(255), created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP);
