GAHA Laravel Backend (raw skeleton)
- This is a Laravel project skeleton that includes M-P<PERSON><PERSON> (Daraja) integration service and controllers.
- You MUST run `composer install` to install vendor dependencies.
- Steps:
  1. cp .env.example .env and fill DB + MPESA values
  2. composer install
  3. php artisan key:generate
  4. php artisan migrate (or import database/schema.sql)
  5. Point document root to backend/public
- Daraja setup:
  - Register at developer.safaricom.co.ke and get consumer key/secret, passkey.
  - Use MPESA_SHORTCODE=174379 for sandbox and MPESA_CALLBACK_URL pointing to /api/mpesa/callback
