<?php
namespace App\Http\Controllers;
use App\Models\Payment;
use Illuminate\Http\Request;
use App\Services\MpesaService;
use Illuminate\Support\Facades\Log;
class PaymentsController extends Controller {
    protected $mpesa;
    public function __construct(MpesaService $mpesa){ $this->mpesa = $mpesa; }
    public function initiate(Request $r){
        $amount = $r->input('amount',100);
        $phone = $r->input('phone');
        if(!$phone) return response()->json(['error'=>'phone_required'],400);
        $res = $this->mpesa->stkPush($phone, $amount, 'GAHA', 'Application/Donation');
        if(isset($res['error'])) return response()->json(['error'=>$res],500);
        $checkout_id = $res['CheckoutRequestID'] ?? ('chk_'.bin2hex(random_bytes(6)));
        $p = Payment::create([ 'user_id' => $r->user()->id ?? null, 'type' => $r->input('type','application'), 'provider' => 'mpesa', 'provider_txn_id' => $checkout_id, 'amount' => $amount, 'currency' => 'KES', 'status' => 'pending' ]);
        return response()->json(['status'=>'initiated','checkout_id'=>$checkout_id,'response'=>$res]);
    }
    public function status($checkout_id){
        $p = Payment::where('provider_txn_id',$checkout_id)->first();
        if(!$p) return response()->json(['status'=>'unknown']);
        return response()->json(['status'=>$p->status]);
    }
    public function mpesaCallback(Request $r){
        $body = $r->all();
        Log::info('Mpesa callback: '.json_encode($body));
        $content = $body['Body'] ?? null;
        if($content && isset($content['stkCallback'])){
            $stk = $content['stkCallback'];
            $checkoutId = $stk['CheckoutRequestID'] ?? null;
            $resultCode = $stk['ResultCode'] ?? null;
            if($checkoutId){
                $p = Payment::where('provider_txn_id',$checkoutId)->first();
                if($p){
                    if($resultCode === 0){ $p->status = 'success'; } else { $p->status = 'failed'; }
                    $p->save();
                }
            }
        }
        return response()->json(['received'=>true]);
    }
}
