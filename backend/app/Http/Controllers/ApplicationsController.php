<?php
namespace App\Http\Controllers;
use App\Models\Application;
use Illuminate\Http\Request;
class ApplicationsController extends Controller { public function store(Request $r){ $data = $r->only(['user_id','job_id','cover_letter']); if($r->hasFile('cv')){ $path = $r->file('cv')->store('cvs'); $data['cv_path'] = $path; } $app = Application::create($data); return response()->json(['application'=>$app]); } }
