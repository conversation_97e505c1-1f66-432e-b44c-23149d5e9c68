<?php
namespace App\Services;
use Illuminate\Support\Facades\Http;
class MpesaService {
    protected $consumerKey;
    protected $consumerSecret;
    protected $shortcode;
    protected $passkey;
    protected $env;
    protected $callbackUrl;
    public function __construct(){
        $this->consumerKey = env('MPESA_CONSUMER_KEY');
        $this->consumerSecret = env('MPESA_CONSUMER_SECRET');
        $this->shortcode = env('MPESA_SHORTCODE');
        $this->passkey = env('MPESA_PASSKEY');
        $this->env = env('MPESA_ENV','sandbox');
        $this->callbackUrl = env('MPESA_CALLBACK_URL');
    }
    protected function baseUrl(){ return $this->env === 'production' ? 'https://api.safaricom.co.ke' : 'https://sandbox.safaricom.co.ke'; }
    protected function getToken(){
        $url = $this->baseUrl().'/oauth/v1/generate?grant_type=client_credentials';
        $res = Http::withBasicAuth($this->consumerKey, $this->consumerSecret)->get($url);
        if($res->successful()){ return $res->json()['access_token'] ?? null; }
        return null;
    }
    public function stkPush($phone, $amount, $accountRef='GAHA', $transactionDesc='Payment'){
        $token = $this->getToken();
        if(!$token) return ['error'=>'token_error'];
        $timestamp = now()->format('YmdHis');
        $passwd = base64_encode($this->shortcode.$this->passkey.$timestamp);
        $payload = [
            'BusinessShortCode' => $this->shortcode,
            'Password' => $passwd,
            'Timestamp' => $timestamp,
            'TransactionType' => 'CustomerPayBillOnline',
            'Amount' => intval($amount),
            'PartyA' => $phone,
            'PartyB' => $this->shortcode,
            'PhoneNumber' => $phone,
            'CallBackURL' => $this->callbackUrl,
            'AccountReference' => $accountRef,
            'TransactionDesc' => $transactionDesc
        ];
        $url = $this->baseUrl().'/mpesa/stkpush/v1/processrequest';
        $res = Http::withToken($token)->post($url, $payload);
        if($res->successful()){ return $res->json(); }
        return ['error'=>'request_failed','response'=>$res->body()];
    }
}
