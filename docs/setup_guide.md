# Setup Guide (short)
1. Frontend:
   - cd frontend
   - npm install
   - npm run build
   - upload dist/ contents to public_html/

2. Backend:
   - cd backend
   - cp .env.example .env (edit DB and MPESA keys)
   - composer install
   - php artisan key:generate
   - php artisan migrate OR import database/schema.sql
   - point your domain/subdomain to backend/public

3. <PERSON><PERSON> (M-Pesa):
   - Register for Daraja sandbox and get keys.
   - Set MPESA_CALLBACK_URL in .env and ensure it is reachable.
   - Use test phone numbers provided by Daraja.

4. If your host does not allow composer, run composer install locally and upload vendor/ too.
