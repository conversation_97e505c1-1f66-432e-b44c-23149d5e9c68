# SMS Service Setup Guide for GAFHO

This guide explains how to set up SMS notifications for job applications and donations using Africa's Talking SMS service.

## Prerequisites

1. **Africa's Talking Account**: Sign up at [https://africastalking.com](https://africastalking.com)
2. **API Credentials**: Get your API key and username from the dashboard
3. **SMS Credits**: Purchase SMS credits for sending messages

## Setup Instructions

### 1. Create Africa's Talking Account

1. Visit [https://africastalking.com](https://africastalking.com)
2. Sign up for a new account
3. Verify your email and phone number
4. Complete the account setup process

### 2. Get API Credentials

1. Log into your Africa's Talking dashboard
2. Navigate to "Settings" > "API Keys"
3. Copy your API Key and Username
4. Note: For testing, you can use the sandbox environment

### 3. Configure Environment Variables

1. Copy `.env.example` to `.env` in the frontend directory:
   ```bash
   cp .env.example .env
   ```

2. Update the SMS configuration in `.env`:
   ```
   REACT_APP_AFRICASTALKING_API_KEY=your-actual-api-key
   REACT_APP_AFRICASTALKING_USERNAME=your-actual-username
   ```

### 4. Purchase SMS Credits

1. In your Africa's Talking dashboard, go to "Billing"
2. Add credits to your account (minimum $1 USD)
3. SMS costs approximately $0.01 per message in Kenya

### 5. Test SMS Functionality

1. Start your development server:
   ```bash
   npm run dev
   ```

2. Test job application SMS:
   - Go to `/careers` and apply for a job
   - Complete the payment process
   - Check if SMS is received

3. Test donation SMS:
   - Go to `/donate` and make a donation
   - Complete the payment process
   - Check if SMS confirmation is received

## SMS Features Implemented

### Job Application SMS
- **Confirmation SMS**: Sent when application is submitted
- **Payment Reminder**: Sent if payment fails
- **Status Updates**: Sent when application status changes
- **Interview Invitations**: Sent when candidate is shortlisted

### Donation SMS
- **Confirmation SMS**: Sent when donation is completed
- **Receipt Information**: Includes transaction ID and amount
- **Thank You Message**: Personalized appreciation message

## Admin Portal Access

The admin portal is available at `/admin` and includes:

### Job Management
- Create new job listings
- Edit existing jobs
- Delete job postings
- View application statistics

### Application Management
- View all job applications
- Update application status
- Send SMS notifications to candidates
- Track payment status

### Features
- Real-time SMS notifications
- Application status tracking
- Candidate communication tools
- Job listing management

## SMS Message Templates

### Job Application Confirmation
```
Dear Applicant,

Your application for [Job Title] has been received successfully.

Application ID: [ID]
Application Fee: KES 100

You will receive updates on your application status via SMS and email.

Thank you for your interest in GAFHO.

- Global Action for Humanitarian Organisation
```

### Donation Confirmation
```
Dear Donor,

Thank you for your generous donation of KES [Amount] to GAFHO.

Transaction ID: [ID]
Date: [Date]

Your contribution will make a real difference in the lives of vulnerable communities.

God bless you!

- Global Action for Humanitarian Organisation
```

## Troubleshooting

### Common Issues

1. **SMS Not Sending**
   - Check API credentials in `.env` file
   - Verify SMS credits in your account
   - Check phone number format (+254XXXXXXXXX)

2. **Invalid Phone Number**
   - Ensure phone numbers start with +254
   - Remove any spaces or special characters
   - Use international format

3. **API Errors**
   - Check internet connection
   - Verify API key is correct
   - Ensure account is active

### Error Codes

- **401 Unauthorized**: Invalid API key
- **402 Payment Required**: Insufficient SMS credits
- **400 Bad Request**: Invalid phone number format

## Cost Estimation

- **SMS Cost**: ~$0.01 per message
- **Monthly Estimate**: 
  - 100 applications × 2 SMS each = $2
  - 50 donations × 1 SMS each = $0.50
  - Total: ~$2.50/month for moderate usage

## Security Considerations

1. **Environment Variables**: Never commit `.env` file to version control
2. **API Keys**: Keep API credentials secure and rotate regularly
3. **Phone Numbers**: Validate and sanitize phone numbers before sending
4. **Rate Limiting**: Implement rate limiting to prevent SMS spam

## Support

For technical support:
- **Africa's Talking**: [<EMAIL>](mailto:<EMAIL>)
- **GAFHO Technical**: [<EMAIL>](mailto:<EMAIL>)
- **Documentation**: [https://developers.africastalking.com](https://developers.africastalking.com)

## Next Steps

1. Set up your Africa's Talking account
2. Configure environment variables
3. Test SMS functionality
4. Monitor SMS usage and costs
5. Implement additional SMS features as needed
