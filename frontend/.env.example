# Environment Variables for GAFHO Frontend Application

# Africa's Talking SMS Service Configuration
# Sign up at https://africastalking.com to get your API credentials
REACT_APP_AFRICASTALKING_API_KEY=your-api-key-here
REACT_APP_AFRICASTALKING_USERNAME=your-username-here

# API Configuration
REACT_APP_API_BASE_URL=http://localhost:8000/api

# Application Configuration
REACT_APP_APP_NAME=GAFHO
REACT_APP_APP_VERSION=1.0.0

# Contact Information
REACT_APP_PHONE_NUMBER=+254720740945
REACT_APP_EMAIL=<EMAIL>
REACT_APP_WEBSITE=www.gafho.com
REACT_APP_ADDRESS=Kanu House, 2nd Floor, Rm 34, Nakuru City

# Social Media Links (optional)
REACT_APP_FACEBOOK_URL=https://facebook.com/gafho
REACT_APP_TWITTER_URL=https://twitter.com/gafho
REACT_APP_INSTAGRAM_URL=https://instagram.com/gafho
REACT_APP_LINKEDIN_URL=https://linkedin.com/company/gafho

# Payment Configuration
REACT_APP_MPESA_PAYBILL=247247
REACT_APP_APPLICATION_FEE=100

# Google Analytics (optional)
REACT_APP_GA_TRACKING_ID=your-ga-tracking-id

# Instructions:
# 1. Copy this file to .env in the same directory
# 2. Replace the placeholder values with your actual configuration
# 3. Never commit the .env file to version control
# 4. Restart your development server after making changes
