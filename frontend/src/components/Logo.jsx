import React from 'react'

const Logo = ({ size = 'medium', showText = true, className = '' }) => {
  const sizes = {
    small: { width: 32, height: 32, fontSize: '0.875rem' },
    medium: { width: 40, height: 40, fontSize: '1rem' },
    large: { width: 60, height: 60, fontSize: '1.5rem' }
  }

  const currentSize = sizes[size]

  return (
    <div className={`logo-container ${className}`} style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
      {/* Logo Icon - You can replace this with an actual image */}
      <div 
        className="logo-icon"
        style={{
          width: currentSize.width,
          height: currentSize.height,
          background: 'linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-green) 100%)',
          borderRadius: '12px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontWeight: '900',
          fontSize: currentSize.fontSize,
          boxShadow: '0 4px 12px rgba(30, 64, 175, 0.3)',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        {/* Background pattern */}
        <div 
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'%3E%3Cpath d='M20 20h60v60H20z' fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='2'/%3E%3Ccircle cx='50' cy='50' r='15' fill='none' stroke='rgba(255,255,255,0.1)' stroke-width='2'/%3E%3C/svg%3E")`,
            backgroundSize: '100% 100%'
          }}
        />
        <span style={{ position: 'relative', zIndex: 1 }}>GAFHO</span>
      </div>
      
      {showText && (
        <div className="logo-text">
          <div 
            style={{ 
              fontWeight: '700', 
              fontSize: size === 'large' ? '1.5rem' : '1.125rem',
              color: 'var(--neutral-800)',
              lineHeight: 1.2
            }}
          >
            Global Action for
          </div>
          <div 
            style={{ 
              fontWeight: '600', 
              fontSize: size === 'large' ? '1.25rem' : '1rem',
              color: 'var(--primary-blue)',
              lineHeight: 1.2
            }}
          >
            Humanitarian Organisation
          </div>
        </div>
      )}
    </div>
  )
}

export default Logo
