import React, { useState, useRef, useEffect } from 'react'

const WhatsAppChatbot = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hello! I'm GAFHO's virtual assistant. How can I help you today?",
      sender: 'bot'
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const messagesEndRef = useRef(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const botResponses = {
    'hello': "Hello! Welcome to Global Action for Humanitarian Organisation. How can I assist you today?",
    'hi': "Hi there! I'm here to help with any questions about GAFHO's programs and services.",
    'donate': "To make a donation, you can visit our Donate page or call us at +254 720 740 945. We accept M-Pesa payments for your convenience.",
    'donation': "We accept donations through M-Pesa. Visit our Donate page to make a secure payment and support our humanitarian programs.",
    'job': "We have various career opportunities available. Check our Careers page for current openings and application details.",
    'career': "Visit our Careers page to see available positions. You can apply online with a small application fee of KES 100.",
    'program': "GAFHO runs several programs including Food Security, Education Sponsorship, Emergency Relief, Community Development, Healthcare Access, and Youth Empowerment.",
    'programs': "Our programs include Food Security, Education Sponsorship, Emergency Relief, Community Development, Healthcare Access, and Youth Empowerment. Visit our Programs page for details.",
    'contact': "You can reach us at:\n📧 <EMAIL>\n📞 +254 720 740 945\n🌐 www.gafho.com\n📍 Kanu House, 2nd Floor, Rm 34, Nakuru City",
    'address': "Our head office is located at Kanu House, 2nd Floor, Room 34, Nakuru City, Kenya. Office hours: Monday-Friday 8AM-5PM, Saturday 9AM-1PM.",
    'phone': "You can call us at +254 720 740 945 for general inquiries.",
    'email': "Send us an <NAME_EMAIL> for general inquiries and support.",
    'help': "I can help you with information about:\n• Donations and how to donate\n• Career opportunities\n• Our programs and services\n• Contact information\n• Office hours and location\n\nWhat would you like to know?",
    'default': "I understand you're looking for information. I can help with donations, careers, programs, or contact details. Could you be more specific about what you need?"
  }

  const getBotResponse = (userMessage) => {
    const lowerMessage = userMessage.toLowerCase()
    
    for (const [key, response] of Object.entries(botResponses)) {
      if (lowerMessage.includes(key)) {
        return response
      }
    }
    
    return botResponses.default
  }

  const handleSendMessage = (e) => {
    e.preventDefault()
    if (!inputValue.trim()) return

    const userMessage = {
      id: messages.length + 1,
      text: inputValue,
      sender: 'user'
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')

    // Simulate bot response delay
    setTimeout(() => {
      const botResponse = {
        id: messages.length + 2,
        text: getBotResponse(inputValue),
        sender: 'bot'
      }
      setMessages(prev => [...prev, botResponse])
    }, 1000)
  }

  const quickReplies = [
    "How to donate?",
    "Career opportunities",
    "Our programs",
    "Contact information"
  ]

  const handleQuickReply = (reply) => {
    const userMessage = {
      id: messages.length + 1,
      text: reply,
      sender: 'user'
    }

    setMessages(prev => [...prev, userMessage])

    setTimeout(() => {
      const botResponse = {
        id: messages.length + 2,
        text: getBotResponse(reply),
        sender: 'bot'
      }
      setMessages(prev => [...prev, botResponse])
    }, 1000)
  }

  return (
    <div className="whatsapp-chatbot">
      <button 
        className="chatbot-toggle"
        onClick={() => setIsOpen(!isOpen)}
      >
        <svg width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
        </svg>
      </button>
      
      {isOpen && (
        <div className="chatbot-window open">
          <div className="chatbot-header">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
              <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
            </svg>
            <div>
              <div style={{fontWeight: '600'}}>GAFHO Assistant</div>
              <div style={{fontSize: '0.75rem', opacity: 0.8}}>Online now</div>
            </div>
          </div>
          
          <div className="chatbot-messages">
            {messages.map((message) => (
              <div 
                key={message.id} 
                className={`chatbot-message ${message.sender}`}
              >
                {message.text}
              </div>
            ))}
            
            {messages.length === 1 && (
              <div style={{marginTop: '1rem'}}>
                <div style={{fontSize: '0.75rem', color: '#666', marginBottom: '0.5rem'}}>
                  Quick replies:
                </div>
                {quickReplies.map((reply, index) => (
                  <button
                    key={index}
                    onClick={() => handleQuickReply(reply)}
                    style={{
                      display: 'block',
                      width: '100%',
                      marginBottom: '0.5rem',
                      padding: '0.5rem',
                      background: '#f0f0f0',
                      border: '1px solid #ddd',
                      borderRadius: '6px',
                      cursor: 'pointer',
                      fontSize: '0.75rem',
                      textAlign: 'left'
                    }}
                  >
                    {reply}
                  </button>
                ))}
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>
          
          <form className="chatbot-input" onSubmit={handleSendMessage}>
            <input
              type="text"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Type your message..."
            />
            <button type="submit">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
              </svg>
            </button>
          </form>
        </div>
      )}
    </div>
  )
}

export default WhatsAppChatbot
