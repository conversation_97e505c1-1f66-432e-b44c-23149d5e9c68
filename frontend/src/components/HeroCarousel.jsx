import React, { useState, useEffect, useCallback } from 'react'
import { Link } from 'react-router-dom'

const HeroCarousel = () => {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)
  const [isTransitioning, setIsTransitioning] = useState(false)

  const slides = [
    {
      id: 1,
      title: "Transforming Lives Through Humanitarian Action",
      subtitle: "Global Action for Humanitarian Organisation (GAFHO) is dedicated to providing essential support to vulnerable communities through food assistance, education sponsorship, and emergency relief programs across Kenya and East Africa.",
      image: "https://images.unsplash.com/photo-1593113598332-cd288d649433?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      cta1: "Make a Donation",
      cta2: "Our Programs",
      gradient: "linear-gradient(135deg, rgba(30, 64, 175, 0.8), rgba(5, 150, 105, 0.6))"
    },
    {
      id: 2,
      title: "Emergency Relief When Communities Need It Most",
      subtitle: "When disaster strikes, we respond quickly with emergency relief including shelter, medical supplies, and immediate assistance to help communities recover and rebuild.",
      image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      cta1: "Emergency Support",
      cta2: "Learn More",
      gradient: "linear-gradient(135deg, rgba(234, 88, 12, 0.8), rgba(30, 64, 175, 0.6))"
    },
    {
      id: 3,
      title: "Education: The Key to Breaking the Cycle of Poverty",
      subtitle: "Education is the foundation of a better future. We sponsor children's education, provide school supplies, and support educational infrastructure in underserved communities.",
      image: "https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2073&q=80",
      cta1: "Sponsor Education",
      cta2: "Our Impact",
      gradient: "linear-gradient(135deg, rgba(5, 150, 105, 0.8), rgba(234, 88, 12, 0.6))"
    },
    {
      id: 4,
      title: "Food Security for Vulnerable Families",
      subtitle: "We provide essential food assistance to vulnerable families, ensuring no child goes to bed hungry. Our programs include emergency food distribution and sustainable nutrition support.",
      image: "https://images.unsplash.com/photo-1593113598332-cd288d649433?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80",
      cta1: "Support Families",
      cta2: "View Programs",
      gradient: "linear-gradient(135deg, rgba(30, 64, 175, 0.8), rgba(234, 88, 12, 0.6))"
    }
  ]

  const nextSlide = useCallback(() => {
    setIsTransitioning(true)
    setCurrentSlide((prev) => (prev + 1) % slides.length)
    setTimeout(() => setIsTransitioning(false), 300)
  }, [slides.length])

  const prevSlide = useCallback(() => {
    setIsTransitioning(true)
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length)
    setTimeout(() => setIsTransitioning(false), 300)
  }, [slides.length])

  const goToSlide = useCallback((index) => {
    if (index !== currentSlide) {
      setIsTransitioning(true)
      setCurrentSlide(index)
      setTimeout(() => setIsTransitioning(false), 300)
    }
  }, [currentSlide])

  useEffect(() => {
    if (!isPlaying) return

    const timer = setInterval(nextSlide, 6000)
    return () => clearInterval(timer)
  }, [isPlaying, nextSlide])

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  const handleKeyDown = useCallback((event) => {
    if (event.key === 'ArrowLeft') prevSlide()
    if (event.key === 'ArrowRight') nextSlide()
    if (event.key === ' ') {
      event.preventDefault()
      togglePlayPause()
    }
  }, [prevSlide, nextSlide])

  useEffect(() => {
    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])

  return (
    <div className="hero-carousel" role="region" aria-label="Hero carousel">
      <div className="carousel-container">
        {slides.map((slide, index) => (
          <div
            key={slide.id}
            className={`carousel-slide ${index === currentSlide ? 'active' : ''} ${isTransitioning ? 'transitioning' : ''}`}
            style={{
              backgroundImage: `url(${slide.image})`,
              transform: `translateX(${(index - currentSlide) * 100}%)`
            }}
            aria-hidden={index !== currentSlide}
          >
            <div
              className="slide-overlay"
              style={{ background: slide.gradient }}
            />
          </div>
        ))}

        <div className="carousel-content">
          <div className="hero-content">
            <div className="hero-text">
              <h1 className="hero-title">
                {slides[currentSlide].title}
              </h1>
              <p className="hero-subtitle">
                {slides[currentSlide].subtitle}
              </p>
            </div>
            <div className="hero-actions">
              <Link
                to="/donate"
                className="btn btn-primary btn-hero"
                aria-label={slides[currentSlide].cta1}
              >
                {slides[currentSlide].cta1}
              </Link>
              <Link
                to="/programs"
                className="btn btn-secondary btn-hero"
                aria-label={slides[currentSlide].cta2}
              >
                {slides[currentSlide].cta2}
              </Link>
            </div>
          </div>
        </div>

        {/* Navigation Controls */}
        <button
          className="carousel-nav carousel-nav-prev"
          onClick={prevSlide}
          aria-label="Previous slide"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>

        <button
          className="carousel-nav carousel-nav-next"
          onClick={nextSlide}
          aria-label="Next slide"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
            <path d="M9 18L15 12L9 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </button>

        {/* Play/Pause Button */}
        <button
          className="carousel-play-pause"
          onClick={togglePlayPause}
          aria-label={isPlaying ? 'Pause carousel' : 'Play carousel'}
        >
          {isPlaying ? (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <rect x="6" y="4" width="4" height="16" fill="currentColor"/>
              <rect x="14" y="4" width="4" height="16" fill="currentColor"/>
            </svg>
          ) : (
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <polygon points="5,3 19,12 5,21" fill="currentColor"/>
            </svg>
          )}
        </button>
      </div>

      {/* Slide Indicators */}
      <div className="carousel-indicators" role="tablist">
        {slides.map((slide, index) => (
          <button
            key={index}
            className={`carousel-indicator ${index === currentSlide ? 'active' : ''}`}
            onClick={() => goToSlide(index)}
            role="tab"
            aria-selected={index === currentSlide}
            aria-label={`Go to slide ${index + 1}: ${slide.title}`}
          />
        ))}
      </div>
    </div>
  )
}

export default HeroCarousel
