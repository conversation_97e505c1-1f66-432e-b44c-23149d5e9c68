import React from 'react'
import { createRoot } from 'react-dom/client'
import { <PERSON>rowser<PERSON><PERSON>er, Routes, Route, Link } from 'react-router-dom'
import Home from './pages/Home'
import About from './pages/About'
import Programs from './pages/Programs'
import Donate from './pages/Donate'
import Careers from './pages/Careers'
import Contact from './pages/Contact'
import Apply from './pages/Apply'
import AdminDashboard from './pages/AdminDashboard'
import WhatsAppChatbot from './components/WhatsAppChatbot'
import Logo from './components/Logo'
import './styles.css'

function App() {
  return (
    <BrowserRouter>
      {/* Top Navigation Bar */}
      <div className='top-nav'>
        <div className='top-nav-content'>
          <div className='top-nav-left'>
            <a href="tel:+254720740945" className='phone-link'>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/>
              </svg>
              +254 720 740 945
            </a>
            <span style={{color: 'var(--neutral-600)', fontSize: '0.75rem'}}>
              Call for any inquiry
            </span>
          </div>
          <div className='top-nav-right'>
            <Link to='/careers' className='cta-nav-btn careers'>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 6h-2.18c.11-.31.18-.65.18-1a2.996 2.996 0 0 0-5.5-1.65l-.5.67-.5-.68C10.96 2.54 10.05 2 9 2 7.34 2 6 3.34 6 5c0 .35.07.69.18 1H4c-1.11 0-2 .89-2 2v11c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2zM9 4c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm6 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1z"/>
              </svg>
              Careers
            </Link>
            <Link to='/donate' className='cta-nav-btn donate'>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z"/>
              </svg>
              Donate Now
            </Link>
          </div>
        </div>
      </div>

      {/* Main Navigation */}
      <nav className='nav'>
        <div className='nav-content'>
          <Logo size="medium" showText={true} />
          <ul className='nav-links'>
            <li><Link to='/'>Home</Link></li>
            <li><Link to='/about'>About</Link></li>
            <li><Link to='/programs'>Programs</Link></li>
            <li><Link to='/donate'>Donate</Link></li>
            <li><Link to='/careers'>Careers</Link></li>
            <li><Link to='/contact'>Contact</Link></li>
          </ul>
        </div>
      </nav>

      <Routes>
        <Route path='/' element={<Home/>}/>
        <Route path='/about' element={<About/>}/>
        <Route path='/programs' element={<Programs/>}/>
        <Route path='/donate' element={<Donate/>}/>
        <Route path='/careers' element={<Careers/>}/>
        <Route path='/careers/apply/:id' element={<Apply/>}/>
        <Route path='/contact' element={<Contact/>}/>
        <Route path='/admin' element={<AdminDashboard/>}/>
      </Routes>

      {/* Floating WhatsApp Elements */}
      <WhatsAppChatbot />

      <a
        href="https://wa.me/254720740945"
        target="_blank"
        rel="noopener noreferrer"
        className="floating-whatsapp"
        style={{bottom: '8rem'}}
      >
        <div className="whatsapp-icon">
          <svg width="28" height="28" viewBox="0 0 24 24" fill="currentColor">
            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.465 3.488"/>
          </svg>
        </div>
      </a>
    </BrowserRouter>
  )
}

createRoot(document.getElementById('root')).render(<App />)
