import React, { useState, useEffect, useCallback } from 'react'
import { Link } from 'react-router-dom'
import HeroCarousel from '../components/HeroCarousel'

// Custom hook for animated counters
const useAnimatedCounter = (targetValue, startValue = 0, duration = 2000, delay = 500) => {
  const [count, setCount] = useState(startValue)

  useEffect(() => {
    const animateCount = () => {
      const steps = 60
      const stepDuration = duration / steps
      const increment = (targetValue - startValue) / steps

      let currentStep = 0
      const timer = setInterval(() => {
        currentStep++
        const progress = currentStep / steps
        const easeOutQuart = 1 - Math.pow(1 - progress, 4) // Smooth easing

        setCount(Math.floor(startValue + (targetValue - startValue) * easeOutQuart))

        if (currentStep >= steps) {
          clearInterval(timer)
          setCount(targetValue)
        }
      }, stepDuration)

      return timer
    }

    const delayTimer = setTimeout(animateCount, delay)
    return () => clearTimeout(delayTimer)
  }, [targetValue, startValue, duration, delay])

  return count
}

// Impact Stats Component
const ImpactStats = () => {
  const familiesCount = useAnimatedCounter(500, 100, 2000, 500)
  const childrenCount = useAnimatedCounter(150, 100, 2000, 700)
  const emergenciesCount = useAnimatedCounter(50, 100, 2000, 900)

  const stats = [
    {
      count: familiesCount,
      label: 'Families Supported',
      description: 'Providing essential food and household support',
      color: 'var(--primary-blue)',
      delay: '0s'
    },
    {
      count: childrenCount,
      label: 'Children Educated',
      description: 'Education sponsorship and school support',
      color: 'var(--primary-green)',
      delay: '0.2s'
    },
    {
      count: emergenciesCount,
      label: 'Emergency Responses',
      description: 'Rapid relief during crisis situations',
      color: 'var(--accent-orange)',
      delay: '0.4s'
    }
  ]

  return (
    <section className="impact-stats-section">
      <div className="container">
        <div className="stats-grid">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="stat-card animate-fadeInUp"
              style={{ animationDelay: stat.delay }}
            >
              <div className="stat-number" style={{ color: stat.color }}>
                {stat.count}+
              </div>
              <h3 className="stat-label">{stat.label}</h3>
              <p className="stat-description">{stat.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

// Mission Section Component
const MissionSection = () => {
  const missions = [
    {
      title: 'Food Security',
      description: 'We provide essential food assistance to vulnerable families, ensuring no child goes to bed hungry. Our programs include emergency food distribution and sustainable nutrition support.',
      color: 'var(--primary-blue)',
      icon: '🍽️'
    },
    {
      title: 'Education Access',
      description: 'Education is the key to breaking the cycle of poverty. We sponsor children\'s education, provide school supplies, and support educational infrastructure in underserved communities.',
      color: 'var(--primary-green)',
      icon: '📚'
    },
    {
      title: 'Emergency Relief',
      description: 'When disaster strikes, we respond quickly with emergency relief including shelter, medical supplies, and immediate assistance to help communities recover and rebuild.',
      color: 'var(--accent-orange)',
      icon: '🚨'
    }
  ]

  return (
    <section className="mission-section">
      <div className="container">
        <div className="section-header">
          <h2 className="section-title">Our Mission</h2>
          <p className="section-subtitle">
            To provide comprehensive humanitarian support that empowers communities
            and creates sustainable pathways out of poverty and crisis.
          </p>
        </div>
        <div className="mission-grid">
          {missions.map((mission, index) => (
            <div key={index} className="mission-card">
              <div className="mission-icon">{mission.icon}</div>
              <h3 className="mission-title" style={{ color: mission.color }}>
                {mission.title}
              </h3>
              <p className="mission-description">{mission.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

// Call to Action Component
const CallToActionSection = () => {
  return (
    <section className="cta-section">
      <div className="container">
        <div className="cta-content">
          <h2 className="cta-title">Join Our Mission</h2>
          <p className="cta-subtitle">
            Your support makes a real difference in the lives of vulnerable families.
            Whether through donations, volunteering, or career opportunities,
            you can be part of positive change.
          </p>
          <div className="cta-buttons">
            <Link to="/donate" className="btn btn-primary btn-large">
              Donate Now
            </Link>
            <Link to="/careers" className="btn btn-outline btn-large">
              Join Our Team
            </Link>
          </div>
        </div>
      </div>
    </section>
  )
}

// Footer Component with updated contact info from the image
const Footer = () => {
  const contactInfo = {
    phone: '+254 720 740 945',
    email: '<EMAIL>',
    website: 'www.gafho.com',
    address: 'Kanu House, 2nd Floor, Rm 34, Nakuru City'
  }

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          <div className="footer-section">
            <h3 className="footer-title">Global Action for Humanitarian Organisation</h3>
            <p className="footer-description">
              Dedicated to transforming lives through comprehensive humanitarian
              support and community development programs.
            </p>
          </div>
          <div className="footer-section">
            <h3 className="footer-title">Quick Links</h3>
            <div className="footer-links">
              <Link to="/about" className="footer-link">About Us</Link>
              <Link to="/programs" className="footer-link">Our Programs</Link>
              <Link to="/careers" className="footer-link">Careers</Link>
              <Link to="/contact" className="footer-link">Contact</Link>
            </div>
          </div>
          <div className="footer-section">
            <h3 className="footer-title">Contact Info</h3>
            <div className="contact-info">
              <p className="contact-item">
                <span className="contact-icon">📧</span>
                <a href={`mailto:${contactInfo.email}`}>{contactInfo.email}</a>
              </p>
              <p className="contact-item">
                <span className="contact-icon">📞</span>
                <a href={`tel:${contactInfo.phone}`}>{contactInfo.phone}</a>
              </p>
              <p className="contact-item">
                <span className="contact-icon">🌐</span>
                <a href={`https://${contactInfo.website}`} target="_blank" rel="noopener noreferrer">
                  {contactInfo.website}
                </a>
              </p>
              <p className="contact-item">
                <span className="contact-icon">📍</span>
                {contactInfo.address}
              </p>
            </div>
          </div>
        </div>
        <div className="footer-bottom">
          <p>&copy; 2024 Global Action for Humanitarian Organisation. All rights reserved.</p>
        </div>
      </div>
    </footer>
  )
}

export default function Home() {
  return (
    <main className="home-page">
      <HeroCarousel />
      <ImpactStats />
      <MissionSection />
      <CallToActionSection />
      <Footer />
    </main>
  )
}


