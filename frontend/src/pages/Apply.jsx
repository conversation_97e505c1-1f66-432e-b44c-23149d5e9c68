import React, {useState,useEffect} from 'react'
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom'
import axios from 'axios'
import { API_BASE } from '../config'
import smsService from '../services/smsService'

export default function Apply() {
  const {id} = useParams()
  const [job, setJob] = useState(null)
  const [status, setStatus] = useState('idle')
  const [phone, setPhone] = useState('')
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    phone: '',
    experience: '',
    coverLetter: ''
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchJob = async () => {
      try {
        setLoading(true)
        const response = await axios.get(API_BASE + '/jobs/' + id)
        setJob(response.data.job)
      } catch (error) {
        console.error('Error fetching job:', error)
        // Set a sample job for demo purposes
        setJob({
          id: id,
          title: "Program Manager - Food Security",
          description: "Lead our food security programs, managing distribution networks and community partnerships across multiple regions.",
          location: "Nairobi, Kenya",
          slots: 2,
          open_until: "2024-12-31",
          type: "Full-time",
          experience: "3-5 years"
        })
      } finally {
        setLoading(false)
      }
    }

    fetchJob()
  }, [id])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  async function handlePay() {
    if (!formData.phone || !formData.fullName || !formData.email) {
      alert('Please fill in all required fields')
      return
    }

    setStatus('initiating')
    try {
      const res = await axios.post(API_BASE + '/payments/initiate', {
        type: 'application',
        amount: 100,
        phone: formData.phone,
        jobTitle: job.title,
        applicantName: formData.fullName,
        applicantEmail: formData.email
      })

      if (res.data.checkout_id) {
        setStatus('waiting')

        // Generate application ID
        const applicationId = `GAFHO-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`

        // Send SMS confirmation
        try {
          await smsService.sendJobApplicationConfirmation(
            formData.phone,
            job.title,
            applicationId
          )
          console.log('SMS confirmation sent successfully')
        } catch (smsError) {
          console.error('Failed to send SMS:', smsError)
          // Don't fail the application if SMS fails
        }

        alert(`M-Pesa STK Push sent to your phone. Please complete the payment.\n\nApplication ID: ${applicationId}\n\nYou will receive an SMS confirmation shortly.`)

        // Store application data locally for now
        localStorage.setItem('lastApplication', JSON.stringify({
          applicationId,
          jobTitle: job.title,
          applicantName: formData.fullName,
          applicantEmail: formData.email,
          phone: formData.phone,
          status: 'payment_pending',
          timestamp: new Date().toISOString()
        }))

      } else {
        setStatus('failed')
        alert('Payment initiation failed. Please try again.')
      }
    } catch (e) {
      setStatus('failed')
      console.error('Payment error:', e)

      // Send payment reminder SMS if phone number is valid
      if (formData.phone) {
        try {
          await smsService.sendPaymentReminder(formData.phone, job.title, 100)
        } catch (smsError) {
          console.error('Failed to send payment reminder SMS:', smsError)
        }
      }

      alert('Error processing payment. Please try again. A payment reminder has been sent to your phone.')
    }
  }

  if (loading) {
    return (
      <section className="section">
        <div className="container text-center">
          <div className="loading" style={{margin: '2rem auto'}}></div>
          <p>Loading job details...</p>
        </div>
      </section>
    )
  }

  if (!job) {
    return (
      <section className="section">
        <div className="container text-center">
          <h2>Job Not Found</h2>
          <p>The job you're looking for doesn't exist or has been removed.</p>
          <Link to="/careers" className="btn btn-primary">
            Browse Other Jobs
          </Link>
        </div>
      </section>
    )
  }

  return (
    <>
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1>Apply for Position</h1>
          <p>
            Complete your application for the {job.title} position. 
            Follow the steps below to submit your application.
          </p>
        </div>
      </section>

      {/* Job Details */}
      <section className="section">
        <div className="container">
          <div className="card">
            <h2 style={{color: 'var(--primary-blue)', marginBottom: '1rem'}}>{job.title}</h2>
            <div style={{
              display: 'flex',
              gap: '1rem',
              marginBottom: '1.5rem',
              flexWrap: 'wrap'
            }}>
              <span style={{
                backgroundColor: 'var(--neutral-100)',
                padding: '0.5rem 1rem',
                borderRadius: '6px',
                fontSize: '0.875rem'
              }}>
                📍 {job.location}
              </span>
              <span style={{
                backgroundColor: 'var(--neutral-100)',
                padding: '0.5rem 1rem',
                borderRadius: '6px',
                fontSize: '0.875rem'
              }}>
                💼 {job.type || 'Full-time'}
              </span>
            </div>
            <div style={{marginBottom: '1.5rem'}}>
              <h3 style={{marginBottom: '0.5rem', color: 'var(--neutral-800)'}}>Job Description</h3>
              <p style={{lineHeight: '1.6'}}>{job.description}</p>
            </div>
            <div style={{
              backgroundColor: 'var(--neutral-50)',
              padding: '1rem',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <p style={{fontSize: '1.125rem', fontWeight: '600', color: 'var(--primary-blue)'}}>
                Application Fee: KES 100
              </p>
              <p style={{fontSize: '0.875rem', color: 'var(--neutral-600)'}}>
                Pay via M-Pesa to complete your application
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Application Form */}
      <section className="section" style={{backgroundColor: 'var(--neutral-50)'}}>
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">Application Form</h2>
            <p className="section-subtitle">
              Fill out the form below and complete payment to submit your application.
            </p>
          </div>
          
          <div style={{maxWidth: '800px', margin: '0 auto'}}>
            <div className="card">
              <form onSubmit={(e) => { e.preventDefault(); handlePay(); }}>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                  gap: '1.5rem',
                  marginBottom: '1.5rem'
                }}>
                  <div className="form-group">
                    <label className="form-label">Full Name *</label>
                    <input
                      type="text"
                      name="fullName"
                      className="form-input"
                      value={formData.fullName}
                      onChange={handleInputChange}
                      placeholder="Enter your full name"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">Email Address *</label>
                    <input
                      type="email"
                      name="email"
                      className="form-input"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label">Phone Number *</label>
                  <input
                    type="tel"
                    name="phone"
                    className="form-input"
                    value={formData.phone}
                    onChange={handleInputChange}
                    placeholder="2547XXXXXXXX"
                    required
                  />
                  <small style={{color: 'var(--neutral-600)', fontSize: '0.875rem'}}>
                    Enter your M-Pesa registered number
                  </small>
                </div>

                <div className="form-group">
                  <label className="form-label">Cover Letter</label>
                  <textarea
                    name="coverLetter"
                    className="form-input form-textarea"
                    value={formData.coverLetter}
                    onChange={handleInputChange}
                    placeholder="Tell us why you're interested in this position..."
                    rows="6"
                  />
                </div>

                <button
                  type="submit"
                  className="btn btn-primary"
                  style={{width: '100%', fontSize: '1.125rem', padding: '1rem'}}
                  disabled={status === 'initiating' || status === 'waiting'}
                >
                  {status === 'initiating' ? (
                    <>
                      <span className="loading"></span>
                      Processing Payment...
                    </>
                  ) : status === 'waiting' ? (
                    'Waiting for Payment...'
                  ) : (
                    'Pay & Submit Application'
                  )}
                </button>

                {status && (
                  <div style={{
                    marginTop: '1rem',
                    padding: '1rem',
                    backgroundColor: 'var(--neutral-50)',
                    borderRadius: '8px',
                    textAlign: 'center',
                    fontWeight: '600'
                  }}>
                    Status: {status}
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Back to Careers */}
      <section className="section">
        <div className="container text-center">
          <Link to="/careers" className="btn btn-outline">
            ← Back to All Jobs
          </Link>
        </div>
      </section>
    </>
  )
}