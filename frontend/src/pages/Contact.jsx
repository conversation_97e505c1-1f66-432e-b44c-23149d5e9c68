import React, {useState} from 'react'

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: ''
  })
  const [status, setStatus] = useState('idle')

  const handleSubmit = (e) => {
    e.preventDefault()
    setStatus('submitting')
    
    // Simulate form submission
    setTimeout(() => {
      setStatus('success')
      setFormData({ name: '', email: '', phone: '', subject: '', message: '' })
    }, 2000)
  }

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  return (
    <>
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1>Get in Touch</h1>
          <p>
            We'd love to hear from you. Whether you have questions about our programs, 
            want to partner with us, or need support, we're here to help.
          </p>
        </div>
      </section>

      {/* Contact Information */}
      <section className="section">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">Contact Information</h2>
            <p className="section-subtitle">
              Reach out to us through any of these channels. We're here to help and answer your questions.
            </p>
          </div>
          <div className="card-grid">
            <div className="card text-center">
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>📧</div>
              <h3 style={{color: 'var(--primary-blue)', marginBottom: '1rem'}}>Email Us</h3>
              <p style={{marginBottom: '1rem'}}>
                <strong>General Inquiries:</strong><br/>
                <EMAIL>
              </p>
              <p style={{marginBottom: '1rem'}}>
                <strong>Partnerships:</strong><br/>
                <EMAIL>
              </p>
              <p>
                <strong>Media & Press:</strong><br/>
                <EMAIL>
              </p>
            </div>
            <div className="card text-center">
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>📞</div>
              <h3 style={{color: 'var(--primary-green)', marginBottom: '1rem'}}>Call Us</h3>
              <p style={{marginBottom: '1rem'}}>
                <strong>Main Office:</strong><br/>
                +254 700 000 000
              </p>
              <p style={{marginBottom: '1rem'}}>
                <strong>Emergency Line:</strong><br/>
                +254 700 000 001
              </p>
              <p>
                <strong>WhatsApp:</strong><br/>
                +254 700 000 002
              </p>
            </div>
            <div className="card text-center">
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>📍</div>
              <h3 style={{color: 'var(--accent-orange)', marginBottom: '1rem'}}>Visit Us</h3>
              <p style={{marginBottom: '1rem'}}>
                <strong>Head Office:</strong><br/>
                Global Humanitarian Action<br/>
                Westlands Business Centre<br/>
                Nairobi, Kenya
              </p>
              <p>
                <strong>Office Hours:</strong><br/>
                Monday - Friday: 8:00 AM - 5:00 PM<br/>
                Saturday: 9:00 AM - 1:00 PM
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="section" style={{backgroundColor: 'var(--neutral-50)'}}>
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">Send Us a Message</h2>
            <p className="section-subtitle">
              Fill out the form below and we'll get back to you as soon as possible.
            </p>
          </div>
          
          <div style={{maxWidth: '800px', margin: '0 auto'}}>
            <div className="card">
              <form onSubmit={handleSubmit}>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                  gap: '1.5rem',
                  marginBottom: '1.5rem'
                }}>
                  <div className="form-group">
                    <label className="form-label">Full Name *</label>
                    <input
                      type="text"
                      name="name"
                      className="form-input"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="Enter your full name"
                      required
                    />
                  </div>
                  <div className="form-group">
                    <label className="form-label">Email Address *</label>
                    <input
                      type="email"
                      name="email"
                      className="form-input"
                      value={formData.email}
                      onChange={handleChange}
                      placeholder="Enter your email"
                      required
                    />
                  </div>
                </div>

                <div className="form-group">
                  <label className="form-label">Message *</label>
                  <textarea
                    name="message"
                    className="form-input form-textarea"
                    value={formData.message}
                    onChange={handleChange}
                    placeholder="Tell us how we can help you..."
                    rows="6"
                    required
                  />
                </div>

                <button
                  type="submit"
                  className="btn btn-primary"
                  style={{width: '100%', fontSize: '1.125rem', padding: '1rem'}}
                  disabled={status === 'submitting'}
                >
                  {status === 'submitting' ? (
                    <>
                      <span className="loading"></span>
                      Sending Message...
                    </>
                  ) : status === 'success' ? (
                    'Message Sent Successfully!'
                  ) : (
                    'Send Message'
                  )}
                </button>

                {status === 'success' && (
                  <div style={{
                    marginTop: '1rem',
                    padding: '1rem',
                    backgroundColor: '#d1fae5',
                    color: '#065f46',
                    borderRadius: '8px',
                    textAlign: 'center',
                    fontWeight: '600'
                  }}>
                    Thank you for your message! We'll get back to you within 24 hours.
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}