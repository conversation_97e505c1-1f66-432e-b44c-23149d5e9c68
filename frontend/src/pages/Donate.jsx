import React, {useState} from 'react'
import axios from 'axios'
import { API_BASE } from '../config'
import smsService from '../services/smsService'

export default function Donate() {
  const [phone, setPhone] = useState('')
  const [amount, setAmount] = useState(100)
  const [status, setStatus] = useState('idle')
  const [message, setMessage] = useState('')
  const [donorName, setDonorName] = useState('')

  const donationAmounts = [100, 500, 1000, 2500, 5000, 10000]

  async function handleDonate() {
    if (!phone || !donorName) {
      alert('Please fill in all required fields')
      return
    }

    if (!phone.startsWith('254')) {
      alert('Please enter a valid Kenyan phone number starting with 254')
      return
    }

    setStatus('initiating')
    try {
      const res = await axios.post(API_BASE + '/payments/initiate', {
        type: 'donation',
        amount: amount,
        phone: phone,
        donor_name: donorName,
        message: message
      })
      
      if (res.data.checkout_id) {
        setStatus('waiting')

        // Generate transaction ID
        const transactionId = `GAFHO-DON-${Date.now()}-${Math.random().toString(36).substr(2, 9).toUpperCase()}`

        // Send SMS confirmation
        try {
          await smsService.sendDonationConfirmation(
            phone,
            amount,
            transactionId
          )
          console.log('Donation SMS confirmation sent successfully')
        } catch (smsError) {
          console.error('Failed to send donation SMS:', smsError)
          // Don't fail the donation if SMS fails
        }

        alert(`M-Pesa STK Push sent to your phone. Please complete the payment on your mobile device.\n\nTransaction ID: ${transactionId}\n\nYou will receive an SMS confirmation shortly.`)

        // Store donation data locally for tracking
        localStorage.setItem('lastDonation', JSON.stringify({
          transactionId,
          amount,
          donorName,
          phone,
          message,
          status: 'payment_pending',
          timestamp: new Date().toISOString()
        }))

      } else {
        setStatus('failed')
        alert('Could not initiate payment. Please try again.')
      }
    } catch (e) {
      setStatus('failed')
      alert('Error initiating payment. Please check your connection and try again.')
    }
  }

  const getStatusMessage = () => {
    switch (status) {
      case 'initiating':
        return 'Initiating payment...'
      case 'waiting':
        return 'Waiting for payment confirmation...'
      case 'success':
        return 'Payment successful! Thank you for your donation.'
      case 'failed':
        return 'Payment failed. Please try again.'
      default:
        return ''
    }
  }

  const getStatusColor = () => {
    switch (status) {
      case 'initiating':
      case 'waiting':
        return 'var(--primary-blue)'
      case 'success':
        return 'var(--primary-green)'
      case 'failed':
        return 'var(--accent-orange)'
      default:
        return 'var(--neutral-600)'
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1>Make a Difference Today</h1>
          <p>
            Your donation helps us provide essential support to vulnerable families, 
            sponsor children's education, and respond to humanitarian crises.
          </p>
        </div>
      </section>

      {/* Donation Impact */}
      <section className="section">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">Your Impact</h2>
            <p className="section-subtitle">
              See how your donation makes a real difference in the lives of vulnerable communities.
            </p>
          </div>
          <div className="card-grid">
            <div className="card text-center">
              <h3 style={{fontSize: '2rem', color: 'var(--primary-blue)', marginBottom: '0.5rem'}}>KES 100</h3>
              <p style={{fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem'}}>Feeds a Family</p>
              <p style={{color: 'var(--neutral-600)'}}>Provides essential food for a family for one week</p>
            </div>
            <div className="card text-center">
              <h3 style={{fontSize: '2rem', color: 'var(--primary-green)', marginBottom: '0.5rem'}}>KES 500</h3>
              <p style={{fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem'}}>School Supplies</p>
              <p style={{color: 'var(--neutral-600)'}}>Covers school supplies and materials for one child</p>
            </div>
            <div className="card text-center">
              <h3 style={{fontSize: '2rem', color: 'var(--accent-orange)', marginBottom: '0.5rem'}}>KES 1,000</h3>
              <p style={{fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem'}}>Emergency Relief</p>
              <p style={{color: 'var(--neutral-600)'}}>Provides emergency shelter and medical supplies</p>
            </div>
            <div className="card text-center">
              <h3 style={{fontSize: '2rem', color: 'var(--primary-blue)', marginBottom: '0.5rem'}}>KES 2,500</h3>
              <p style={{fontSize: '1.125rem', fontWeight: '600', marginBottom: '0.5rem'}}>Monthly Support</p>
              <p style={{color: 'var(--neutral-600)'}}>Supports a family's monthly needs and expenses</p>
            </div>
          </div>
        </div>
      </section>

      {/* Donation Form */}
      <section className="section" style={{backgroundColor: 'var(--neutral-50)'}}>
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">Make Your Donation</h2>
            <p className="section-subtitle">
              Support our humanitarian mission with a secure M-Pesa payment.
            </p>
          </div>
          
          <div style={{maxWidth: '600px', margin: '0 auto'}}>
            <div className="card">
              <form onSubmit={(e) => { e.preventDefault(); handleDonate(); }}>
                <div className="form-group">
                  <label className="form-label">Your Name *</label>
                  <input
                    type="text"
                    className="form-input"
                    value={donorName}
                    onChange={e => setDonorName(e.target.value)}
                    placeholder="Enter your full name"
                    required
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">Mobile Number *</label>
                  <input
                    type="tel"
                    className="form-input"
                    value={phone}
                    onChange={e => setPhone(e.target.value)}
                    placeholder="2547XXXXXXXX"
                    required
                  />
                  <small style={{color: 'var(--neutral-600)', fontSize: '0.875rem'}}>
                    Enter your M-Pesa registered number in format 2547XXXXXXXX
                  </small>
                </div>

                <div className="form-group">
                  <label className="form-label">Donation Amount (KES) *</label>
                  <div style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fit, minmax(100px, 1fr))',
                    gap: '0.5rem',
                    marginBottom: '1rem'
                  }}>
                    {donationAmounts.map(amt => (
                      <button
                        key={amt}
                        type="button"
                        onClick={() => setAmount(amt)}
                        style={{
                          padding: '0.75rem',
                          border: amount === amt ? '2px solid var(--primary-blue)' : '2px solid var(--neutral-300)',
                          borderRadius: '8px',
                          backgroundColor: amount === amt ? 'var(--primary-blue)' : 'var(--white)',
                          color: amount === amt ? 'var(--white)' : 'var(--neutral-700)',
                          cursor: 'pointer',
                          fontWeight: '600'
                        }}
                      >
                        {amt.toLocaleString()}
                      </button>
                    ))}
                  </div>
                  <input
                    type="number"
                    className="form-input"
                    value={amount}
                    onChange={e => setAmount(parseInt(e.target.value) || 0)}
                    placeholder="Enter custom amount"
                    min="10"
                    max="100000"
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">Message (Optional)</label>
                  <textarea
                    className="form-input form-textarea"
                    value={message}
                    onChange={e => setMessage(e.target.value)}
                    placeholder="Add a personal message with your donation"
                    rows="3"
                  />
                </div>

                <div style={{
                  backgroundColor: 'var(--neutral-100)',
                  padding: '1rem',
                  borderRadius: '8px',
                  marginBottom: '1.5rem'
                }}>
                  <h4 style={{marginBottom: '0.5rem', color: 'var(--neutral-800)'}}>Donation Summary</h4>
                  <p><strong>Amount:</strong> KES {amount.toLocaleString()}</p>
                  <p><strong>Payment Method:</strong> M-Pesa</p>
                  <p><strong>Processing Fee:</strong> Free</p>
                </div>

                <button
                  type="submit"
                  className="btn btn-primary"
                  style={{width: '100%', fontSize: '1.125rem', padding: '1rem'}}
                  disabled={status === 'initiating' || status === 'waiting'}
                >
                  {status === 'initiating' ? (
                    <>
                      <span className="loading"></span>
                      Processing...
                    </>
                  ) : status === 'waiting' ? (
                    'Waiting for Payment...'
                  ) : (
                    'Donate with M-Pesa'
                  )}
                </button>

                {status && (
                  <div style={{
                    marginTop: '1rem',
                    padding: '1rem',
                    backgroundColor: 'var(--neutral-50)',
                    borderRadius: '8px',
                    textAlign: 'center',
                    color: getStatusColor(),
                    fontWeight: '600'
                  }}>
                    {getStatusMessage()}
                  </div>
                )}
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* Why Donate */}
      <section className="section">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">Why Donate to GAHA?</h2>
            <p className="section-subtitle">
              Your contribution directly supports our humanitarian mission and creates lasting impact.
            </p>
          </div>
          <div className="card-grid">
            <div className="card text-center">
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>🎯</div>
              <h3 style={{color: 'var(--primary-blue)', marginBottom: '1rem'}}>Direct Impact</h3>
              <p>
                Your donation goes directly to programs that support vulnerable families, 
                with minimal administrative overhead.
              </p>
            </div>
            <div className="card text-center">
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>🔒</div>
              <h3 style={{color: 'var(--primary-green)', marginBottom: '1rem'}}>Secure & Transparent</h3>
              <p>
                We maintain the highest standards of financial transparency and 
                accountability in all our operations.
              </p>
            </div>
            <div className="card text-center">
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>📊</div>
              <h3 style={{color: 'var(--accent-orange)', marginBottom: '1rem'}}>Measurable Results</h3>
              <p>
                We provide regular updates on how your donations are being used 
                and the impact they're creating.
              </p>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}