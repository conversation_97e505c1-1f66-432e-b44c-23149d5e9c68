import React, {useState,useEffect} from 'react'
import { <PERSON> } from 'react-router-dom'
import axios from 'axios'
import { API_BASE } from '../config'

export default function Careers() {
  const [jobs, setJobs] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchJobs = async () => {
      try {
        setLoading(true)
        const response = await axios.get(API_BASE + '/jobs')
        setJobs(response.data.jobs || [])
        setError(null)
      } catch (err) {
        console.error('Error fetching jobs:', err)
        setError('Failed to load job listings. Please try again later.')
        // Set some sample jobs for demo purposes
        setJobs([
          {
            id: 1,
            title: "Program Manager - Food Security",
            description: "Lead our food security programs, managing distribution networks and community partnerships across multiple regions.",
            location: "Nairobi, Kenya",
            slots: 2,
            open_until: "2024-12-31",
            type: "Full-time",
            experience: "3-5 years"
          },
          {
            id: 2,
            title: "Education Coordinator",
            description: "Coordinate education sponsorship programs, work with schools and families to ensure children receive quality education.",
            location: "Mombasa, Kenya",
            slots: 1,
            open_until: "2024-12-15",
            type: "Full-time",
            experience: "2-4 years"
          },
          {
            id: 3,
            title: "Emergency Response Specialist",
            description: "Lead rapid response efforts during humanitarian crises, coordinating relief operations and community support.",
            location: "Kisumu, Kenya",
            slots: 1,
            open_until: "2024-11-30",
            type: "Full-time",
            experience: "5+ years"
          }
        ])
      } finally {
        setLoading(false)
      }
    }

    fetchJobs()
  }, [])

  if (loading) {
    return (
      <section className="section">
        <div className="container text-center">
          <div className="loading" style={{margin: '2rem auto'}}></div>
          <p>Loading job opportunities...</p>
        </div>
      </section>
    )
  }

  return (
    <>
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1>Join Our Mission</h1>
          <p>
            Be part of a team that's making a real difference in the lives of vulnerable communities. 
            Explore career opportunities with Global Humanitarian Action.
          </p>
        </div>
      </section>

      {/* Job Listings */}
      <section className="section">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">Current Opportunities</h2>
            <p className="section-subtitle">
              {error && (
                <div style={{
                  backgroundColor: '#fee2e2',
                  color: '#991b1b',
                  padding: '1rem',
                  borderRadius: '8px',
                  marginBottom: '2rem'
                }}>
                  {error}
                </div>
              )}
              Discover exciting career opportunities to join our humanitarian mission. 
              Click "Apply" to start your application process.
            </p>
          </div>
          
          <div className="card-grid">
            {jobs.map(job => (
              <div key={job.id} className="job-card">
                <h3 className="job-title">{job.title}</h3>
                <div className="job-location">📍 {job.location}</div>
                <div className="job-description">{job.description}</div>
                <div style={{marginTop: '1.5rem'}}>
                  <Link to={`/careers/apply/${job.id}`} className="btn btn-primary">
                    Apply Now - KES 100
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </>
  )
}