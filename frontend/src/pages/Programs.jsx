import React from 'react'
import { Link } from 'react-router-dom'

export default function Programs() {
  const programs = [
    {
      id: 1,
      title: "Food & Household Support",
      icon: "🍽️",
      image: "https://images.unsplash.com/photo-1593113598332-cd288d649433?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      description: "Providing essential food assistance and household support to vulnerable families facing food insecurity.",
      details: [
        "Monthly food distribution to 200+ families",
        "Nutritional support for children and pregnant women",
        "Household items and hygiene supplies",
        "Emergency food assistance during crisis"
      ],
      impact: "500+ families supported",
      color: "var(--primary-blue)"
    },
    {
      id: 2,
      title: "Education Sponsorship",
      icon: "📚",
      image: "https://images.unsplash.com/photo-1497486751825-1233686d5d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      description: "Ensuring access to quality education for children from disadvantaged backgrounds through comprehensive sponsorship programs.",
      details: [
        "School fees and educational materials",
        "Uniform and school supplies provision",
        "Mentorship and academic support",
        "Scholarship programs for higher education"
      ],
      impact: "150+ children sponsored",
      color: "var(--primary-green)"
    },
    {
      id: 3,
      title: "Emergency Relief",
      icon: "🚨",
      image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      description: "Rapid response to natural disasters, conflicts, and humanitarian crises with immediate relief and recovery support.",
      details: [
        "Emergency shelter and temporary housing",
        "Medical supplies and healthcare access",
        "Clean water and sanitation facilities",
        "Psychosocial support and counseling"
      ],
      impact: "50+ emergency responses",
      color: "var(--accent-orange)"
    },
    {
      id: 4,
      title: "Community Development",
      icon: "🏘️",
      image: "https://images.unsplash.com/photo-**********-cd4628902d4a?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      description: "Building resilient communities through sustainable development programs that create long-term positive change.",
      details: [
        "Skills training and livelihood programs",
        "Women's empowerment initiatives",
        "Community infrastructure development",
        "Environmental conservation projects"
      ],
      impact: "25+ communities transformed",
      color: "var(--primary-blue)"
    },
    {
      id: 5,
      title: "Healthcare Access",
      icon: "🏥",
      image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      description: "Improving healthcare access and outcomes for underserved communities through medical support and health education.",
      details: [
        "Mobile health clinics in remote areas",
        "Maternal and child health programs",
        "Health education and awareness campaigns",
        "Medical equipment and supplies provision"
      ],
      impact: "10,000+ people reached",
      color: "var(--primary-green)"
    },
    {
      id: 6,
      title: "Youth Empowerment",
      icon: "👥",
      image: "https://images.unsplash.com/photo-1529156069898-49953e39b3ac?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
      description: "Empowering young people through education, skills development, and leadership opportunities for a brighter future.",
      details: [
        "Youth leadership training programs",
        "Vocational skills development",
        "Entrepreneurship and business training",
        "Sports and recreational activities"
      ],
      impact: "300+ youth empowered",
      color: "var(--accent-orange)"
    }
  ]

  return (
    <>
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1>Our Humanitarian Programs</h1>
          <p>
            Discover how our comprehensive programs are making a real difference 
            in the lives of vulnerable communities across Kenya and East Africa.
          </p>
        </div>
      </section>

      {/* Programs Overview */}
      <section className="section">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">Comprehensive Humanitarian Support</h2>
            <p className="section-subtitle">
              Our programs address the most critical needs of vulnerable communities, 
              from immediate relief to long-term development and empowerment.
            </p>
          </div>
          <div className="card-grid">
            {programs.map((program, index) => (
              <div key={program.id} className="card animate-fadeInUp" style={{animationDelay: `${index * 0.1}s`}}>
                <div style={{
                  height: '200px',
                  backgroundImage: `url(${program.image})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  borderRadius: '8px',
                  marginBottom: '1rem',
                  position: 'relative',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    position: 'absolute',
                    top: '0.5rem',
                    right: '0.5rem',
                    fontSize: '2rem',
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    borderRadius: '50%',
                    width: '50px',
                    height: '50px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    {program.icon}
                  </div>
                </div>
                <h3 style={{
                  color: program.color,
                  marginBottom: '1rem',
                  textAlign: 'center'
                }}>
                  {program.title}
                </h3>
                <p style={{
                  marginBottom: '1.5rem',
                  lineHeight: '1.6'
                }}>
                  {program.description}
                </p>
                <div style={{marginBottom: '1.5rem'}}>
                  <h4 style={{
                    fontSize: '1rem',
                    fontWeight: '600',
                    marginBottom: '0.75rem',
                    color: 'var(--neutral-700)'
                  }}>
                    Key Activities:
                  </h4>
                  <ul style={{
                    listStyle: 'none',
                    padding: 0
                  }}>
                    {program.details.map((detail, index) => (
                      <li key={index} style={{
                        padding: '0.25rem 0',
                        position: 'relative',
                        paddingLeft: '1.5rem'
                      }}>
                        <span style={{
                          position: 'absolute',
                          left: 0,
                          color: program.color,
                          fontWeight: 'bold'
                        }}>
                          •
                        </span>
                        {detail}
                      </li>
                    ))}
                  </ul>
                </div>
                <div style={{
                  padding: '1rem',
                  backgroundColor: 'var(--neutral-50)',
                  borderRadius: '8px',
                  textAlign: 'center',
                  fontWeight: '600',
                  color: program.color
                }}>
                  Impact: {program.impact}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Program Impact */}
      <section className="section" style={{backgroundColor: 'var(--neutral-50)'}}>
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">Our Impact in Numbers</h2>
            <p className="section-subtitle">
              The measurable difference our programs are making in communities across the region.
            </p>
          </div>
          <div className="card-grid">
            <div className="card text-center">
              <h3 style={{fontSize: '3rem', color: 'var(--primary-blue)', marginBottom: '0.5rem'}}>500+</h3>
              <p style={{fontSize: '1.25rem', fontWeight: '600', marginBottom: '0.5rem'}}>Families Supported</p>
              <p style={{color: 'var(--neutral-600)'}}>Through food assistance and household support programs</p>
            </div>
            <div className="card text-center">
              <h3 style={{fontSize: '3rem', color: 'var(--primary-green)', marginBottom: '0.5rem'}}>150+</h3>
              <p style={{fontSize: '1.25rem', fontWeight: '600', marginBottom: '0.5rem'}}>Children Educated</p>
              <p style={{color: 'var(--neutral-600)'}}>Through comprehensive education sponsorship programs</p>
            </div>
            <div className="card text-center">
              <h3 style={{fontSize: '3rem', color: 'var(--accent-orange)', marginBottom: '0.5rem'}}>50+</h3>
              <p style={{fontSize: '1.25rem', fontWeight: '600', marginBottom: '0.5rem'}}>Emergency Responses</p>
              <p style={{color: 'var(--neutral-600)'}}>Rapid relief during crisis situations and disasters</p>
            </div>
            <div className="card text-center">
              <h3 style={{fontSize: '3rem', color: 'var(--primary-blue)', marginBottom: '0.5rem'}}>25+</h3>
              <p style={{fontSize: '1.25rem', fontWeight: '600', marginBottom: '0.5rem'}}>Communities Transformed</p>
              <p style={{color: 'var(--neutral-600)'}}>Through sustainable development initiatives</p>
            </div>
          </div>
        </div>
      </section>

      {/* How We Work */}
      <section className="section">
        <div className="container">
          <div className="section-header">
            <h2 className="section-title">How We Work</h2>
            <p className="section-subtitle">
              Our approach combines immediate relief with long-term development to create sustainable change.
            </p>
          </div>
          <div className="card-grid">
            <div className="card text-center">
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>🔍</div>
              <h3 style={{color: 'var(--primary-blue)', marginBottom: '1rem'}}>Assessment</h3>
              <p>
                We conduct thorough needs assessments to understand the specific 
                challenges and opportunities in each community we serve.
              </p>
            </div>
            <div className="card text-center">
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>🤝</div>
              <h3 style={{color: 'var(--primary-green)', marginBottom: '1rem'}}>Collaboration</h3>
              <p>
                We work closely with local communities, government agencies, 
                and other organizations to maximize our collective impact.
              </p>
            </div>
            <div className="card text-center">
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>⚡</div>
              <h3 style={{color: 'var(--accent-orange)', marginBottom: '1rem'}}>Implementation</h3>
              <p>
                Our experienced team implements programs efficiently and effectively, 
                ensuring resources reach those who need them most.
              </p>
            </div>
            <div className="card text-center">
              <div style={{fontSize: '3rem', marginBottom: '1rem'}}>📊</div>
              <h3 style={{color: 'var(--primary-blue)', marginBottom: '1rem'}}>Monitoring</h3>
              <p>
                We continuously monitor and evaluate our programs to ensure 
                they are meeting their objectives and making real impact.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="section" style={{backgroundColor: 'var(--neutral-50)'}}>
        <div className="container text-center">
          <h2 className="section-title">Support Our Programs</h2>
          <p className="section-subtitle mb-4">
            Your support enables us to expand our programs and reach more communities in need. 
            Every contribution makes a real difference in the lives of vulnerable families.
          </p>
          <div className="cta-buttons">
            <Link to="/donate" className="btn btn-primary">
              Donate to Our Programs
            </Link>
            <Link to="/contact" className="btn btn-outline">
              Partner With Us
            </Link>
          </div>
        </div>
      </section>
    </>
  )
}