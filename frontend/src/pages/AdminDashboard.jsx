import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import axios from 'axios'
import { API_BASE } from '../config'
import smsService from '../services/smsService'

export default function AdminDashboard() {
  const [activeTab, setActiveTab] = useState('jobs')
  const [jobs, setJobs] = useState([])
  const [applications, setApplications] = useState([])
  const [loading, setLoading] = useState(false)
  const [showJobForm, setShowJobForm] = useState(false)
  const [editingJob, setEditingJob] = useState(null)
  const [jobForm, setJobForm] = useState({
    title: '',
    description: '',
    location: '',
    type: 'Full-time',
    experience: '',
    slots: 1,
    open_until: ''
  })

  // Sample data for demo
  const sampleJobs = [
    {
      id: 1,
      title: "Program Manager - Food Security",
      description: "Lead our food security programs, managing distribution networks and community partnerships across multiple regions.",
      location: "Nairobi, Kenya",
      slots: 2,
      open_until: "2024-12-31",
      type: "Full-time",
      experience: "3-5 years",
      status: "active",
      applications_count: 15
    },
    {
      id: 2,
      title: "Education Coordinator",
      description: "Coordinate education sponsorship programs, work with schools and families to ensure children receive quality education.",
      location: "Mombasa, Kenya",
      slots: 1,
      open_until: "2024-12-15",
      type: "Full-time",
      experience: "2-4 years",
      status: "active",
      applications_count: 8
    },
    {
      id: 3,
      title: "Emergency Response Specialist",
      description: "Lead rapid response efforts during humanitarian crises, coordinating relief operations and community support.",
      location: "Kisumu, Kenya",
      slots: 1,
      open_until: "2024-11-30",
      type: "Full-time",
      experience: "5+ years",
      status: "closed",
      applications_count: 23
    }
  ]

  const sampleApplications = [
    {
      id: 1,
      job_id: 1,
      job_title: "Program Manager - Food Security",
      applicant_name: "John Doe",
      applicant_email: "<EMAIL>",
      applicant_phone: "+254712345678",
      status: "pending_review",
      applied_at: "2024-01-15T10:30:00Z",
      payment_status: "completed"
    },
    {
      id: 2,
      job_id: 1,
      job_title: "Program Manager - Food Security",
      applicant_name: "Jane Smith",
      applicant_email: "<EMAIL>",
      applicant_phone: "+254723456789",
      status: "shortlisted",
      applied_at: "2024-01-14T14:20:00Z",
      payment_status: "completed"
    },
    {
      id: 3,
      job_id: 2,
      job_title: "Education Coordinator",
      applicant_name: "Peter Kamau",
      applicant_email: "<EMAIL>",
      applicant_phone: "+254734567890",
      status: "interviewed",
      applied_at: "2024-01-13T09:15:00Z",
      payment_status: "completed"
    }
  ]

  useEffect(() => {
    loadData()
  }, [activeTab])

  const loadData = async () => {
    setLoading(true)
    try {
      if (activeTab === 'jobs') {
        // In a real app, this would fetch from API
        setJobs(sampleJobs)
      } else if (activeTab === 'applications') {
        // In a real app, this would fetch from API
        setApplications(sampleApplications)
      }
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleJobFormChange = (e) => {
    const { name, value } = e.target
    setJobForm(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleJobSubmit = async (e) => {
    e.preventDefault()
    try {
      if (editingJob) {
        // Update existing job
        const updatedJobs = jobs.map(job => 
          job.id === editingJob.id ? { ...job, ...jobForm } : job
        )
        setJobs(updatedJobs)
        alert('Job updated successfully!')
      } else {
        // Create new job
        const newJob = {
          ...jobForm,
          id: Date.now(),
          status: 'active',
          applications_count: 0
        }
        setJobs([...jobs, newJob])
        alert('Job created successfully!')
      }
      
      setShowJobForm(false)
      setEditingJob(null)
      setJobForm({
        title: '',
        description: '',
        location: '',
        type: 'Full-time',
        experience: '',
        slots: 1,
        open_until: ''
      })
    } catch (error) {
      console.error('Error saving job:', error)
      alert('Error saving job. Please try again.')
    }
  }

  const handleEditJob = (job) => {
    setEditingJob(job)
    setJobForm({
      title: job.title,
      description: job.description,
      location: job.location,
      type: job.type,
      experience: job.experience,
      slots: job.slots,
      open_until: job.open_until
    })
    setShowJobForm(true)
  }

  const handleDeleteJob = async (jobId) => {
    if (window.confirm('Are you sure you want to delete this job?')) {
      setJobs(jobs.filter(job => job.id !== jobId))
      alert('Job deleted successfully!')
    }
  }

  const handleApplicationStatusUpdate = async (applicationId, newStatus) => {
    try {
      const application = applications.find(app => app.id === applicationId)
      if (!application) return

      // Update application status
      const updatedApplications = applications.map(app =>
        app.id === applicationId ? { ...app, status: newStatus } : app
      )
      setApplications(updatedApplications)

      // Send SMS notification
      let nextSteps = ''
      switch (newStatus) {
        case 'shortlisted':
          nextSteps = 'You have been shortlisted! We will contact you soon for the next steps.'
          break
        case 'interviewed':
          nextSteps = 'Thank you for the interview. We will get back to you with our decision soon.'
          break
        case 'hired':
          nextSteps = 'Congratulations! You have been selected for this position. Please check your email for next steps.'
          break
        case 'rejected':
          nextSteps = 'Thank you for your interest. Unfortunately, we have decided to proceed with other candidates.'
          break
        default:
          nextSteps = 'Your application status has been updated.'
      }

      await smsService.sendApplicationStatusUpdate(
        application.applicant_phone,
        application.job_title,
        newStatus,
        nextSteps
      )

      alert('Application status updated and SMS sent!')
    } catch (error) {
      console.error('Error updating application status:', error)
      alert('Status updated but SMS failed to send.')
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending_review': return 'var(--accent-orange)'
      case 'shortlisted': return 'var(--primary-blue)'
      case 'interviewed': return 'var(--primary-green)'
      case 'hired': return 'var(--primary-green)'
      case 'rejected': return 'var(--neutral-500)'
      default: return 'var(--neutral-600)'
    }
  }

  return (
    <>
      {/* Hero Section */}
      <section className="hero">
        <div className="hero-content">
          <h1>Admin Dashboard</h1>
          <p>Manage job listings, applications, and candidate communications</p>
        </div>
      </section>

      {/* Dashboard Content */}
      <section className="section">
        <div className="container">
          {/* Tab Navigation */}
          <div style={{
            display: 'flex',
            gap: '1rem',
            marginBottom: '2rem',
            borderBottom: '2px solid var(--neutral-200)',
            paddingBottom: '1rem'
          }}>
            <button
              onClick={() => setActiveTab('jobs')}
              style={{
                padding: '0.75rem 1.5rem',
                border: 'none',
                background: activeTab === 'jobs' ? 'var(--primary-blue)' : 'transparent',
                color: activeTab === 'jobs' ? 'white' : 'var(--neutral-700)',
                borderRadius: '6px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            >
              Job Listings ({jobs.length})
            </button>
            <button
              onClick={() => setActiveTab('applications')}
              style={{
                padding: '0.75rem 1.5rem',
                border: 'none',
                background: activeTab === 'applications' ? 'var(--primary-blue)' : 'transparent',
                color: activeTab === 'applications' ? 'white' : 'var(--neutral-700)',
                borderRadius: '6px',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
            >
              Applications ({applications.length})
            </button>
          </div>

          {/* Jobs Tab */}
          {activeTab === 'jobs' && (
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '2rem' }}>
                <h2>Job Listings</h2>
                <button
                  onClick={() => setShowJobForm(true)}
                  className="btn btn-primary"
                >
                  + Add New Job
                </button>
              </div>

              {loading ? (
                <div className="text-center">
                  <div className="loading" style={{ margin: '2rem auto' }}></div>
                  <p>Loading jobs...</p>
                </div>
              ) : (
                <div className="card-grid">
                  {jobs.map(job => (
                    <div key={job.id} className="card">
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '1rem' }}>
                        <h3 style={{ color: 'var(--primary-blue)', margin: 0 }}>{job.title}</h3>
                        <span style={{
                          padding: '0.25rem 0.75rem',
                          borderRadius: '12px',
                          fontSize: '0.75rem',
                          fontWeight: '600',
                          backgroundColor: job.status === 'active' ? 'var(--primary-green)' : 'var(--neutral-400)',
                          color: 'white'
                        }}>
                          {job.status}
                        </span>
                      </div>
                      <p style={{ color: 'var(--neutral-600)', marginBottom: '1rem', fontSize: '0.875rem' }}>
                        📍 {job.location} • 💼 {job.type} • 👥 {job.slots} positions
                      </p>
                      <p style={{ marginBottom: '1rem', lineHeight: '1.5' }}>
                        {job.description.substring(0, 120)}...
                      </p>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                        <span style={{ fontSize: '0.875rem', color: 'var(--neutral-600)' }}>
                          Applications: {job.applications_count}
                        </span>
                        <span style={{ fontSize: '0.875rem', color: 'var(--neutral-600)' }}>
                          Closes: {new Date(job.open_until).toLocaleDateString()}
                        </span>
                      </div>
                      <div style={{ display: 'flex', gap: '0.5rem' }}>
                        <button
                          onClick={() => handleEditJob(job)}
                          className="btn btn-outline"
                          style={{ flex: 1, fontSize: '0.875rem', padding: '0.5rem' }}
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteJob(job.id)}
                          className="btn"
                          style={{
                            flex: 1,
                            fontSize: '0.875rem',
                            padding: '0.5rem',
                            backgroundColor: 'var(--neutral-400)',
                            color: 'white'
                          }}
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Applications Tab */}
          {activeTab === 'applications' && (
            <div>
              <h2 style={{ marginBottom: '2rem' }}>Job Applications</h2>

              {loading ? (
                <div className="text-center">
                  <div className="loading" style={{ margin: '2rem auto' }}></div>
                  <p>Loading applications...</p>
                </div>
              ) : (
                <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  {applications.map(application => (
                    <div key={application.id} className="card">
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr auto', gap: '1rem', alignItems: 'center' }}>
                        <div>
                          <h4 style={{ margin: '0 0 0.5rem 0', color: 'var(--primary-blue)' }}>
                            {application.applicant_name}
                          </h4>
                          <p style={{ margin: '0', fontSize: '0.875rem', color: 'var(--neutral-600)' }}>
                            {application.job_title}
                          </p>
                          <p style={{ margin: '0.25rem 0 0 0', fontSize: '0.75rem', color: 'var(--neutral-500)' }}>
                            Applied: {new Date(application.applied_at).toLocaleDateString()}
                          </p>
                        </div>

                        <div>
                          <p style={{ margin: '0', fontSize: '0.875rem' }}>
                            📧 {application.applicant_email}
                          </p>
                          <p style={{ margin: '0.25rem 0 0 0', fontSize: '0.875rem' }}>
                            📞 {application.applicant_phone}
                          </p>
                        </div>

                        <div>
                          <span style={{
                            padding: '0.25rem 0.75rem',
                            borderRadius: '12px',
                            fontSize: '0.75rem',
                            fontWeight: '600',
                            backgroundColor: getStatusColor(application.status),
                            color: 'white'
                          }}>
                            {application.status.replace('_', ' ')}
                          </span>
                          <p style={{ margin: '0.25rem 0 0 0', fontSize: '0.75rem', color: 'var(--neutral-500)' }}>
                            Payment: {application.payment_status}
                          </p>
                        </div>

                        <div>
                          <select
                            value={application.status}
                            onChange={(e) => handleApplicationStatusUpdate(application.id, e.target.value)}
                            style={{
                              padding: '0.5rem',
                              borderRadius: '4px',
                              border: '1px solid var(--neutral-300)',
                              fontSize: '0.875rem'
                            }}
                          >
                            <option value="pending_review">Pending Review</option>
                            <option value="shortlisted">Shortlisted</option>
                            <option value="interviewed">Interviewed</option>
                            <option value="hired">Hired</option>
                            <option value="rejected">Rejected</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </section>

      {/* Job Form Modal */}
      {showJobForm && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '2rem'
        }}>
          <div style={{
            backgroundColor: 'white',
            borderRadius: '12px',
            padding: '2rem',
            maxWidth: '600px',
            width: '100%',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h3 style={{ marginBottom: '1.5rem' }}>
              {editingJob ? 'Edit Job' : 'Add New Job'}
            </h3>

            <form onSubmit={handleJobSubmit}>
              <div className="form-group">
                <label className="form-label">Job Title *</label>
                <input
                  type="text"
                  name="title"
                  className="form-input"
                  value={jobForm.title}
                  onChange={handleJobFormChange}
                  required
                />
              </div>

              <div className="form-group">
                <label className="form-label">Description *</label>
                <textarea
                  name="description"
                  className="form-input form-textarea"
                  value={jobForm.description}
                  onChange={handleJobFormChange}
                  rows="4"
                  required
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                <div className="form-group">
                  <label className="form-label">Location *</label>
                  <input
                    type="text"
                    name="location"
                    className="form-input"
                    value={jobForm.location}
                    onChange={handleJobFormChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">Job Type *</label>
                  <select
                    name="type"
                    className="form-input"
                    value={jobForm.type}
                    onChange={handleJobFormChange}
                    required
                  >
                    <option value="Full-time">Full-time</option>
                    <option value="Part-time">Part-time</option>
                    <option value="Contract">Contract</option>
                    <option value="Volunteer">Volunteer</option>
                  </select>
                </div>
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1rem' }}>
                <div className="form-group">
                  <label className="form-label">Experience Required</label>
                  <input
                    type="text"
                    name="experience"
                    className="form-input"
                    value={jobForm.experience}
                    onChange={handleJobFormChange}
                    placeholder="e.g., 2-3 years"
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">Number of Positions *</label>
                  <input
                    type="number"
                    name="slots"
                    className="form-input"
                    value={jobForm.slots}
                    onChange={handleJobFormChange}
                    min="1"
                    required
                  />
                </div>
              </div>

              <div className="form-group">
                <label className="form-label">Application Deadline *</label>
                <input
                  type="date"
                  name="open_until"
                  className="form-input"
                  value={jobForm.open_until}
                  onChange={handleJobFormChange}
                  required
                />
              </div>

              <div style={{ display: 'flex', gap: '1rem', justifyContent: 'flex-end' }}>
                <button
                  type="button"
                  onClick={() => {
                    setShowJobForm(false)
                    setEditingJob(null)
                    setJobForm({
                      title: '',
                      description: '',
                      location: '',
                      type: 'Full-time',
                      experience: '',
                      slots: 1,
                      open_until: ''
                    })
                  }}
                  className="btn btn-outline"
                >
                  Cancel
                </button>
                <button type="submit" className="btn btn-primary">
                  {editingJob ? 'Update Job' : 'Create Job'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </>
  )
}
