/* Global Humanitarian Action - Modern CSS */
:root {
  --primary-blue: #1e40af;
  --primary-green: #059669;
  --accent-orange: #ea580c;
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;
  --white: #ffffff;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: var(--neutral-700);
  background-color: var(--white);
}

/* Navigation - Make text black and reduce overlay opacity */
.nav {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(80px);

  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  color: var(--neutral-800);
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.1);
}

/* Top Navigation - More transparent white */
.top-nav {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 0.5rem 0;
  position: relative;
  z-index: 1001;
  color: var(--neutral-700);
}

.nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.brand {
  font-size: 1.5rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo {
  width: 40px;
  height: 40px;
  background: var(--white);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 900;
  color: var(--primary-blue);
  font-size: 1.2rem;
}

.nav-links {
  display: flex;
  gap: 2rem;
  list-style: none;
}

.nav-links a {
  color: var(--neutral-800);
  text-decoration: none;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  position: relative;
}

.nav-links a:hover {
  background-color: rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Hero Section - Reduce height */
.hero {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.3) 100%),
              url('https://images.unsplash.com/photo-1593113598332-cd288d649433?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  color: var(--neutral-800);
  padding: 6rem 0 3rem;
  text-align: center;
  position: relative;
  overflow: hidden;
  margin-top: -120px;
  padding-top: 140px;
  min-height: 60vh;
}

.hero h1 {
  font-size: 4.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
  color: var(--neutral-800);
}

.hero p {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
  color: var(--neutral-700);
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.875rem 2rem;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.btn-primary {
  background: var(--accent-orange);
  color: var(--white);
}

.btn-primary:hover {
  background: #dc2626;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-secondary {
  background: var(--white);
  color: var(--primary-blue);
  border: 2px solid var(--white);
}

.btn-secondary:hover {
  background: transparent;
  color: var(--white);
  transform: translateY(-2px);
}

.btn-outline {
  background: transparent;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.btn-outline:hover {
  background: var(--primary-blue);
  color: var(--white);
  transform: translateY(-2px);
}

/* Cards */
.card {
  background: var(--white);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  border: 1px solid var(--neutral-200);
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin: 3rem 0;
}

/* Sections */
.section {
  padding: 4rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--neutral-800);
  margin-bottom: 1rem;
}

.section-subtitle {
  font-size: 1.125rem;
  color: var(--neutral-600);
  max-width: 600px;
  margin: 0 auto;
}

/* Forms */
.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-weight: 600;
  color: var(--neutral-700);
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--neutral-300);
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--white);
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

/* Job Cards */
.job-card {
  background: var(--white);
  border-radius: 12px;
  padding: 2rem;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--neutral-200);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.job-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-blue), var(--primary-green));
}

.job-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.job-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--neutral-800);
  margin-bottom: 0.5rem;
}

.job-location {
  color: var(--neutral-600);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.job-description {
  color: var(--neutral-600);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

/* Status indicators */
.status {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
}

.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.status-success {
  background: #d1fae5;
  color: #065f46;
}

.status-failed {
  background: #fee2e2;
  color: #991b1b;
}

.status-waiting {
  background: #dbeafe;
  color: #1e40af;
}

/* Footer */
.footer {
  background: var(--neutral-900);
  color: var(--neutral-300);
  padding: 3rem 0 2rem;
  margin-top: 4rem;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-section h3 {
  color: var(--white);
  margin-bottom: 1rem;
  font-size: 1.125rem;
}

.footer-section p,
.footer-section a {
  color: var(--neutral-400);
  text-decoration: none;
  line-height: 1.6;
}

.footer-section a:hover {
  color: var(--white);
}

.footer-bottom {
  border-top: 1px solid var(--neutral-700);
  padding-top: 2rem;
  text-align: center;
  color: var(--neutral-500);
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-content {
    flex-direction: column;
    gap: 1rem;
    padding: 0 1rem;
  }

  .nav-links {
    gap: 1rem;
  }

  .hero h1 {
    font-size: 3rem;
  }

  .hero p {
    font-size: 1.25rem;
  }

  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .section-title {
    font-size: 2rem;
  }

  .card-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .container {
    padding: 0 1rem;
  }
}

/* Loading states */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: var(--white);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Modern WhatsApp Icons */
.floating-whatsapp {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
  animation: float 3s ease-in-out infinite;
}

.whatsapp-icon {
  width: 65px;
  height: 65px;
  background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  position: relative;
  overflow: hidden;
}

.whatsapp-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.whatsapp-icon:hover::before {
  transform: translateX(100%);
}

.whatsapp-icon:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 35px rgba(37, 211, 102, 0.6);
}

.chatbot-toggle {
  width: 65px;
  height: 65px;
  background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  box-shadow: 0 8px 25px rgba(37, 211, 102, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  position: relative;
  overflow: hidden;
}

.chatbot-toggle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.chatbot-toggle:hover::before {
  transform: translateX(100%);
}

.chatbot-toggle:hover {
  transform: scale(1.1) rotate(-5deg);
  box-shadow: 0 12px 35px rgba(37, 211, 102, 0.6);
}

.whatsapp-chatbot {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 999;
}

.chatbot-window {
  position: absolute;
  bottom: 80px;
  right: 0;
  width: 320px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border: 1px solid #e5e5e5;
  overflow: hidden;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.3s ease;
}

.chatbot-window.open {
  transform: translateY(0);
  opacity: 1;
}

.chatbot-header {
  background: #25D366;
  color: white;
  padding: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.chatbot-messages {
  padding: 1rem;
  max-height: 300px;
  overflow-y: auto;
}

.chatbot-message {
  margin-bottom: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  font-size: 0.875rem;
}

.chatbot-message.bot {
  background: #f0f0f0;
  margin-right: 2rem;
}

.chatbot-message.user {
  background: #25D366;
  color: white;
  margin-left: 2rem;
  text-align: right;
}

.chatbot-input {
  padding: 1rem;
  border-top: 1px solid #e5e5e5;
  display: flex;
  gap: 0.5rem;
}

.chatbot-input input {
  flex: 1;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.875rem;
}

.chatbot-input button {
  padding: 0.5rem 1rem;
  background: #25D366;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.875rem;
}

/* Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes slideInLeft {
  from { transform: translateX(-100px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeInUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes countUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-countUp {
  animation: countUp 0.8s ease-out;
}

/* Enhanced Hero Carousel */
.hero-carousel {
  position: relative;
  height: 100vh;
  overflow: hidden;
  background: var(--neutral-900);
}

.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
  will-change: transform;
}

.carousel-slide.active {
  transform: translateX(0) !important;
}

.carousel-slide.transitioning {
  transition-duration: 0.3s;
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.carousel-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  padding: 0 2rem;
}

.hero-content {
  max-width: 1200px;
  text-align: center;
  color: var(--white);
  animation: fadeInUp 1s ease-out;
}

.hero-text {
  margin-bottom: 3rem;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: 1.5rem;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  line-height: 1.6;
  opacity: 0.95;
  max-width: 800px;
  margin: 0 auto;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-actions {
  display: flex;
  gap: 1.5rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-hero {
  padding: 1rem 2.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  min-width: 180px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hero:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.3);
}

/* Navigation Controls */
.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--white);
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 3;
}

.carousel-nav:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-50%) scale(1.1);
}

.carousel-nav-prev {
  left: 2rem;
}

.carousel-nav-next {
  right: 2rem;
}

.carousel-play-pause {
  position: absolute;
  top: 2rem;
  right: 2rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--white);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 3;
}

.carousel-play-pause:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* Slide Indicators */
.carousel-indicators {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 1rem;
  z-index: 3;
}

.carousel-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.6);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.carousel-indicator:hover {
  background: rgba(255, 255, 255, 0.6);
  transform: scale(1.2);
}

.carousel-indicator.active {
  background: var(--white);
  border-color: var(--white);
  transform: scale(1.3);
}

.carousel-indicator.active::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Top Navigation Styles */
.top-nav-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.top-nav-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.top-nav-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.phone-link {
  color: var(--neutral-700);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.phone-link:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.cta-nav-btn {
  padding: 0.5rem 1.5rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cta-nav-btn.donate {
  background: var(--accent-orange);
  color: white;
}

.cta-nav-btn.careers {
  background: var(--primary-blue);
  color: white;
  border: 1px solid var(--primary-blue);
}

.cta-nav-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Enhanced Home Page Components */

/* Impact Stats Section */
.impact-stats-section {
  padding: 6rem 0;
  background: linear-gradient(135deg, var(--neutral-50) 0%, var(--white) 100%);
  position: relative;
  overflow: hidden;
}

.impact-stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23e2e8f0" opacity="0.3"/><circle cx="75" cy="75" r="1" fill="%23cbd5e1" opacity="0.2"/><circle cx="50" cy="10" r="0.5" fill="%23e2e8f0" opacity="0.4"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.5;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.stat-card {
  background: var(--white);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-blue), var(--primary-green), var(--accent-orange));
  transform: scaleX(0);
  transition: transform 0.4s ease;
}

.stat-card:hover::before {
  transform: scaleX(1);
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.stat-number {
  font-size: clamp(3rem, 6vw, 4.5rem);
  font-weight: 900;
  line-height: 1;
  margin-bottom: 1rem;
  background: linear-gradient(135deg, currentColor 0%, currentColor 100%);
  -webkit-background-clip: text;
  background-clip: text;
  position: relative;
}

.stat-label {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--neutral-800);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.stat-description {
  color: var(--neutral-600);
  font-size: 1rem;
  line-height: 1.6;
  max-width: 280px;
  margin: 0 auto;
}

/* Mission Section */
.mission-section {
  padding: 6rem 0;
  background: var(--white);
  position: relative;
}

.mission-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 3rem;
  margin-top: 4rem;
}

.mission-card {
  background: var(--white);
  border-radius: 16px;
  padding: 3rem 2.5rem;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--neutral-200);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.mission-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(30, 64, 175, 0.02) 0%, rgba(5, 150, 105, 0.02) 50%, rgba(234, 88, 12, 0.02) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.mission-card:hover::before {
  opacity: 1;
}

.mission-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 16px 50px rgba(0, 0, 0, 0.12);
  border-color: rgba(30, 64, 175, 0.2);
}

.mission-icon {
  font-size: 3rem;
  margin-bottom: 1.5rem;
  display: block;
}

.mission-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.mission-description {
  color: var(--neutral-600);
  font-size: 1.125rem;
  line-height: 1.7;
}

/* Call to Action Section */
.cta-section {
  padding: 8rem 0;
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-green) 100%);
  color: var(--white);
  text-align: center;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.cta-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.cta-title {
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 800;
  margin-bottom: 2rem;
  line-height: 1.1;
}

.cta-subtitle {
  font-size: 1.25rem;
  line-height: 1.6;
  margin-bottom: 3rem;
  opacity: 0.95;
}

.btn-large {
  padding: 1.25rem 3rem;
  font-size: 1.25rem;
  font-weight: 700;
  min-width: 200px;
  border-radius: 12px;
}

/* Enhanced Footer */
.footer {
  background: linear-gradient(135deg, var(--neutral-900) 0%, var(--neutral-800) 100%);
  color: var(--neutral-300);
  padding: 4rem 0 2rem;
  position: relative;
  overflow: hidden;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--primary-blue) 50%, transparent 100%);
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-title {
  color: var(--white);
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -0.5rem;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--accent-orange);
  border-radius: 1px;
}

.footer-description {
  line-height: 1.7;
  margin-bottom: 1.5rem;
}

.footer-links {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.footer-link {
  color: var(--neutral-400);
  text-decoration: none;
  transition: all 0.3s ease;
  padding: 0.25rem 0;
  position: relative;
}

.footer-link:hover {
  color: var(--white);
  transform: translateX(4px);
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--neutral-400);
  transition: all 0.3s ease;
}

.contact-item:hover {
  color: var(--white);
}

.contact-icon {
  font-size: 1.125rem;
  width: 24px;
  text-align: center;
}

.contact-item a {
  color: inherit;
  text-decoration: none;
  transition: all 0.3s ease;
}

.contact-item a:hover {
  color: var(--accent-orange);
}

.footer-bottom {
  border-top: 1px solid var(--neutral-700);
  padding-top: 2rem;
  text-align: center;
  color: var(--neutral-500);
  font-size: 0.875rem;
}

/* Responsive Design for New Components */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 0 1rem;
  }

  .stat-card {
    padding: 2rem 1.5rem;
  }

  .mission-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .mission-card {
    padding: 2rem 1.5rem;
  }

  .cta-content {
    padding: 0 1rem;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .btn-hero {
    width: 100%;
    max-width: 280px;
  }

  .carousel-nav {
    width: 50px;
    height: 50px;
  }

  .carousel-nav-prev {
    left: 1rem;
  }

  .carousel-nav-next {
    right: 1rem;
  }

  .carousel-play-pause {
    top: 1rem;
    right: 1rem;
    width: 40px;
    height: 40px;
  }
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 0.5rem; }
.mb-2 { margin-bottom: 1rem; }
.mb-3 { margin-bottom: 1.5rem; }
.mb-4 { margin-bottom: 2rem; }
.mt-1 { margin-top: 0.5rem; }
.mt-2 { margin-top: 1rem; }
.mt-3 { margin-top: 1.5rem; }
.mt-4 { margin-top: 2rem; }
